2025-07-30 09:23:10.512 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 09:23:12.523 [main] INFO  c.ebcp.user.UserServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:23:14.406 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 09:23:14.880 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 457 ms. Found 3 JPA repository interfaces.
2025-07-30 09:23:18.187 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-30 09:23:18.196 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-30 09:23:19.592 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8082 (http)
2025-07-30 09:23:19.701 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-30 09:23:19.798 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:23:19.879 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-30 09:23:20.055 [main] INFO  o.a.c.c.C.[.[localhost].[/users] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:23:20.063 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7399 ms
2025-07-30 09:23:21.226 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 09:23:21.699 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.8.Final
2025-07-30 09:23:21.956 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-30 09:23:23.240 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-30 09:23:23.851 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-30 09:23:27.449 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-30 09:23:27.598 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 09:23:28.742 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-30 09:23:30.725 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 09:23:31.421 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a0c0ac95-5c82-4262-9213-d4f7f698c28a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-30 09:23:31.885 [main] INFO  com.ebcp.user.config.WebConfig - 当前工作目录: G:\work\mobile
2025-07-30 09:23:31.886 [main] INFO  com.ebcp.user.config.WebConfig - 测试路径: G:\work\mobile\uploads, 存在: true
2025-07-30 09:23:31.895 [main] INFO  com.ebcp.user.config.WebConfig - 找到有效的上传目录: G:/work/mobile/uploads/
2025-07-30 09:23:31.896 [main] INFO  com.ebcp.user.config.WebConfig - 配置静态资源映射: /uploads/** -> file:G:/work/mobile/uploads/
2025-07-30 09:23:31.898 [main] INFO  com.ebcp.user.config.WebConfig - 静态资源映射配置完成
2025-07-30 09:23:32.374 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-30 09:23:33.283 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-30 09:23:34.233 [main] WARN  c.n.discovery.InstanceInfoReplicator - Ignoring onDemand update due to rate limiter
2025-07-30 09:23:34.235 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8082"]
2025-07-30 09:23:34.275 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8082 (http) with context path '/users'
2025-07-30 09:23:34.302 [main] INFO  c.ebcp.user.UserServiceApplication - Started UserServiceApplication in 27.461 seconds (process running for 29.564)
2025-07-30 09:23:34.316 [main] INFO  c.ebcp.user.config.DataInitializer - 开始初始化测试用户数据...
2025-07-30 09:23:34.466 [main] INFO  c.ebcp.user.config.DataInitializer - 数据库中已有用户数据，跳过初始化
2025-07-30 09:24:13.965 [http-nio-8082-exec-2] INFO  o.a.c.c.C.[.[localhost].[/users] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:24:14.064 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - === 获取当前用户信息接口开始 ===
2025-07-30 09:24:14.088 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 接收到的用户名: admin
2025-07-30 09:24:14.096 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 接收到的用户ID: null
2025-07-30 09:24:14.099 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - === 所有请求头信息 ===
2025-07-30 09:24:14.102 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: accept-encoding = gzip
2025-07-30 09:24:14.106 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: sec-ch-ua-platform = "Windows"
2025-07-30 09:24:14.107 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: accept-language = zh-CN,zh;q=0.9
2025-07-30 09:24:14.109 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: accept = application/json, text/plain, */*
2025-07-30 09:24:14.111 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: sec-ch-ua = "Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"
2025-07-30 09:24:14.112 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36
2025-07-30 09:24:14.113 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: token = eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc1MzkyNTA1MywiaWF0IjoxNzUzODM4NjUzfQ.8QDc8glFhsQ0Ioyif_Kpe1LYjm0UykhU8k2QOj4Y0dk
2025-07-30 09:24:14.114 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: sec-ch-ua-mobile = ?0
2025-07-30 09:24:14.115 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: origin = http://localhost:3050
2025-07-30 09:24:14.116 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: sec-fetch-site = same-site
2025-07-30 09:24:14.117 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: sec-fetch-mode = cors
2025-07-30 09:24:14.120 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: sec-fetch-dest = empty
2025-07-30 09:24:14.121 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: referer = http://localhost:3050/
2025-07-30 09:24:14.122 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: cookie = token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc1MzkyNTA1MywiaWF0IjoxNzUzODM4NjUzfQ.8QDc8glFhsQ0Ioyif_Kpe1LYjm0UykhU8k2QOj4Y0dk; admin_token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc1MzkyNTA1MywiaWF0IjoxNzUzODM4NjUzfQ.8QDc8glFhsQ0Ioyif_Kpe1LYjm0UykhU8k2QOj4Y0dk
2025-07-30 09:24:14.124 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: x-user-name = admin
2025-07-30 09:24:14.124 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: x-route-name = service-user
2025-07-30 09:24:14.125 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: forwarded = proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:65016"
2025-07-30 09:24:14.127 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-for = 0:0:0:0:0:0:0:1
2025-07-30 09:24:14.128 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-proto = http
2025-07-30 09:24:14.129 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-prefix = /gateway
2025-07-30 09:24:14.130 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-port = 8090
2025-07-30 09:24:14.131 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-host = localhost:8090
2025-07-30 09:24:14.131 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: host = localhost:8090
2025-07-30 09:24:14.135 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 请求头: content-length = 0
2025-07-30 09:24:14.136 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - === 请求头信息结束 ===
2025-07-30 09:24:14.137 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 开始通过用户名查找用户: admin
2025-07-30 09:24:14.138 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 首先在系统用户表中查找用户: admin
2025-07-30 09:24:14.173 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 在系统用户表中找到用户: ID=1, 用户名=admin, 真实姓名=系统管理员
2025-07-30 09:24:14.175 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 设置admin系统用户角色和权限
2025-07-30 09:24:14.176 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 构建的系统用户信息: {roles=[admin], avatar=null, enabled=true, realName=系统管理员, lastLoginTime=null, balance=0.0, phone=13800138000, createTime=null, permissions=[*], name=系统管理员, id=1, userType=system, email=<EMAIL>, username=admin, status=active}
2025-07-30 09:24:14.178 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - === 获取当前系统用户信息成功 ===
2025-07-30 10:00:23.779 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - === 获取当前用户信息接口开始 ===
2025-07-30 10:00:23.781 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 接收到的用户名: admin
2025-07-30 10:00:23.782 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 接收到的用户ID: null
2025-07-30 10:00:23.782 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - === 所有请求头信息 ===
2025-07-30 10:00:23.782 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: accept-encoding = gzip
2025-07-30 10:00:23.783 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: sec-ch-ua-platform = "Windows"
2025-07-30 10:00:23.784 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: accept-language = zh-CN,zh;q=0.9
2025-07-30 10:00:23.784 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: accept = application/json, text/plain, */*
2025-07-30 10:00:23.784 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: sec-ch-ua = "Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"
2025-07-30 10:00:23.785 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36
2025-07-30 10:00:23.786 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: token = eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc1MzkyNzIyMywiaWF0IjoxNzUzODQwODIzfQ.L5F51JzkCPXWvaTcaul4mvMbNM1xLIAZ_YVmNdMGq1I
2025-07-30 10:00:23.787 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: sec-ch-ua-mobile = ?0
2025-07-30 10:00:23.787 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: origin = http://localhost:3050
2025-07-30 10:00:23.788 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: sec-fetch-site = same-site
2025-07-30 10:00:23.788 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: sec-fetch-mode = cors
2025-07-30 10:00:23.789 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: sec-fetch-dest = empty
2025-07-30 10:00:23.790 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: referer = http://localhost:3050/
2025-07-30 10:00:23.790 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: cookie = token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc1MzkyNzIyMywiaWF0IjoxNzUzODQwODIzfQ.L5F51JzkCPXWvaTcaul4mvMbNM1xLIAZ_YVmNdMGq1I; admin_token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc1MzkyNzIyMywiaWF0IjoxNzUzODQwODIzfQ.L5F51JzkCPXWvaTcaul4mvMbNM1xLIAZ_YVmNdMGq1I
2025-07-30 10:00:23.791 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: x-user-name = admin
2025-07-30 10:00:23.792 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: x-route-name = service-user
2025-07-30 10:00:23.792 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: forwarded = proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:58140"
2025-07-30 10:00:23.793 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-for = 0:0:0:0:0:0:0:1
2025-07-30 10:00:23.793 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-proto = http
2025-07-30 10:00:23.794 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-prefix = /gateway
2025-07-30 10:00:23.794 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-port = 8090
2025-07-30 10:00:23.795 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-host = localhost:8090
2025-07-30 10:00:23.795 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: host = localhost:8090
2025-07-30 10:00:23.796 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 请求头: content-length = 0
2025-07-30 10:00:23.796 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - === 请求头信息结束 ===
2025-07-30 10:00:23.796 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 开始通过用户名查找用户: admin
2025-07-30 10:00:23.797 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 首先在系统用户表中查找用户: admin
2025-07-30 10:00:23.811 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 在系统用户表中找到用户: ID=1, 用户名=admin, 真实姓名=系统管理员
2025-07-30 10:00:23.812 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 设置admin系统用户角色和权限
2025-07-30 10:00:23.813 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 构建的系统用户信息: {roles=[admin], avatar=null, enabled=true, realName=系统管理员, lastLoginTime=null, balance=0.0, phone=13800138000, createTime=null, permissions=[*], name=系统管理员, id=1, userType=system, email=<EMAIL>, username=admin, status=active}
2025-07-30 10:00:23.813 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - === 获取当前系统用户信息成功 ===
2025-07-30 10:02:53.842 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:02:53.909 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:53.910 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:02:53.911 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:53.911 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:02:53.925 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:02:53.925 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:02:53.926 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:53.926 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:02:53.927 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:53.993 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:02:53.997 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:53.998 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:02:53.998 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:53.999 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:02:54.002 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:02:54.002 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:02:54.003 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:54.003 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:02:54.004 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:54.059 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:02:54.060 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:02:54.068 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:54.068 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:54.069 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:02:54.069 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:02:54.070 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:54.070 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:54.071 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:02:54.072 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:02:54.077 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:02:54.077 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:02:54.077 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:02:54.078 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:02:54.078 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:54.079 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:54.079 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:02:54.079 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:02:54.080 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:54.080 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.653 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:02:59.656 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.657 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:02:59.657 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.657 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:02:59.660 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:02:59.661 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:02:59.661 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:59.662 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:02:59.662 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.679 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:02:59.682 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.682 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:02:59.683 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.683 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:02:59.687 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:02:59.687 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:02:59.688 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:59.688 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:02:59.688 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:59.804 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:02:59.817 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:02:59.829 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.830 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:02:59.831 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.832 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:02:59.843 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.843 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:02:59.844 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:02:59.845 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:02:59.846 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.848 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:02:59.847 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:59.849 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:02:59.850 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:02:59.853 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:02:59.857 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:02:59.860 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:02:59.860 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:02:59.861 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.855 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:03:19.859 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.861 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:03:19.861 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.862 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:03:19.866 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:03:19.866 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:03:19.867 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:19.868 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:03:19.869 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.885 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:03:19.889 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.890 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:03:19.890 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.891 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:03:19.897 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:03:19.898 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:03:19.899 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:19.899 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:03:19.899 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:19.953 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:03:19.957 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:03:19.964 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.965 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:03:19.966 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.967 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:03:19.967 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.969 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.970 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:03:19.971 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:03:19.977 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:03:19.978 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:03:19.978 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:03:19.979 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:03:19.981 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:19.981 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:03:19.980 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:19.983 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:19.983 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:03:19.984 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.452 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:03:28.456 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.456 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:03:28.457 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.457 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:03:28.460 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:03:28.462 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:03:28.462 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:28.462 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:03:28.463 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.472 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:03:28.475 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.475 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:03:28.476 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.477 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:03:28.480 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:03:28.481 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:03:28.481 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:28.482 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:03:28.482 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:28.509 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:03:28.511 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:03:28.520 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.521 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:03:28.523 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.524 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:03:28.522 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.525 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:03:28.527 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.531 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:03:28.530 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:03:28.533 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:03:28.534 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:28.535 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:03:28.536 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:03:28.539 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:03:28.540 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:03:28.542 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:03:28.545 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:03:28.546 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.432 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:05:55.439 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.439 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:05:55.440 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.443 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:05:55.446 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:05:55.447 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:05:55.447 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:05:55.448 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:05:55.449 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.458 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:05:55.463 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.464 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:05:55.464 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.465 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:05:55.469 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:05:55.470 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:05:55.470 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:05:55.471 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:05:55.471 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:05:55.502 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:05:55.503 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:05:55.511 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.512 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.512 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:05:55.513 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:05:55.513 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.515 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:05:55.514 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.516 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:05:55.520 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:05:55.520 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:05:55.522 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:05:55.522 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:05:55.522 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:05:55.523 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:05:55.524 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:05:55.523 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:05:55.524 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:05:55.528 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.144 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:02.149 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.149 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:02.150 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.150 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:02.153 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:02.153 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:02.154 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:02.154 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:02.154 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.162 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:02.166 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.166 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:02.167 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.167 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:02.170 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:02.170 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:02.171 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:02.171 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:02.171 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:02.212 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:02.213 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:02.219 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.220 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:02.221 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.222 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:02.222 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.223 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:02.221 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.224 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:02.227 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:02.228 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:02.229 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:02.229 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:02.229 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:02.228 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:02.231 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:02.233 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:02.233 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:02.234 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:12.921 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:12.925 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:12.926 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:12.926 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:12.926 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:12.929 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:12.930 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:12.930 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:12.931 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:12.931 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:12.940 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:12.945 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:12.945 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:12.946 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:12.946 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:12.951 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:12.952 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:12.952 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:12.953 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:12.953 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:13.001 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:13.002 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:13.008 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:13.010 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:13.011 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:13.009 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:13.012 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:13.012 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:13.013 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:13.014 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:13.017 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:13.017 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:13.019 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:13.021 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:13.020 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:13.024 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:13.026 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:13.029 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:13.031 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:13.032 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.405 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:17.410 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.412 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:17.412 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.413 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:17.415 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:17.415 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:17.416 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:17.416 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:17.416 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.428 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:17.433 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.433 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:17.434 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.434 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:17.439 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:17.440 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:17.441 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:17.441 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:17.442 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:17.466 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:17.469 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:17.477 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.479 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:17.479 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.480 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.480 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:17.481 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:17.481 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.482 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:17.486 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:17.488 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:17.488 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:17.488 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:17.488 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:17.489 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:17.489 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:17.490 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.490 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:17.491 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.715 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:06:17.730 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.730 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:17.731 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:17.731 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:17.736 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:17.737 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:17.737 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:17.738 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:17.738 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:30.851 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:06:30.855 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:30.855 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:30.856 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:30.856 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:30.859 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:30.859 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:30.860 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:30.860 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:30.860 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:30.865 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:06:30.868 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:30.868 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:30.869 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:30.869 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:30.871 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:30.871 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:30.872 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:30.872 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:30.873 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:30.988 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:06:30.990 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:06:30.995 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:30.998 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:30.998 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:30.999 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:31.000 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:30.999 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:31.002 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:31.003 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:31.005 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:31.006 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:31.006 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:31.007 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:31.007 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:31.010 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:31.011 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:31.013 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:31.014 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:31.014 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:31.071 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:06:31.075 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:31.075 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:31.075 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:31.076 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:31.078 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:31.079 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:31.079 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:31.079 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:31.079 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:36.965 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:06:36.972 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:36.972 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:36.972 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:36.973 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:36.975 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:36.976 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:36.976 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:36.976 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:36.977 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:36.982 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:06:36.985 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:36.985 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:36.985 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:36.986 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:36.988 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:36.988 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:36.989 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:36.989 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:36.989 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:37.007 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:06:37.011 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:37.011 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:37.011 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:37.012 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:37.014 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:37.014 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:37.014 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:37.015 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:37.015 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:37.206 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:06:42.126 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:06:42.131 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:42.132 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:06:42.132 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:42.133 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:06:42.136 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:06:42.136 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:06:42.136 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:06:42.137 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:06:42.137 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:06:42.245 [http-nio-8082-exec-4] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:06:42.701 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:06:42.715 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:06:42.824 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:09:21.948 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:09:21.960 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:09:21.960 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:09:21.960 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:09:21.961 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:09:21.963 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:09:21.963 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:09:21.964 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:09:21.964 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:09:21.965 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:09:22.019 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:09:22.047 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:09:22.058 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:09:22.087 [http-nio-8082-exec-5] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:10:15.196 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:10:15.200 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:15.200 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:15.200 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:15.201 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:15.203 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:15.204 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:15.204 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:15.204 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:15.204 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:15.210 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:10:15.213 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:15.213 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:15.214 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:15.214 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:15.216 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:15.216 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:15.217 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:15.217 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:15.217 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:32.820 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:10:32.824 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.824 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:32.825 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.826 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:32.829 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:32.829 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:32.830 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:32.830 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:32.830 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.835 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:10:32.838 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.838 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:32.839 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.839 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:32.841 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:32.842 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:32.843 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:32.843 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:32.843 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:32.864 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:10:32.867 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:10:32.868 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.868 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:32.869 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.870 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:32.872 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.873 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:32.874 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:32.874 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.874 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:32.875 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:32.876 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:32.878 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:32.879 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.881 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:32.881 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:32.882 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:32.882 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:32.883 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.945 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:10:32.949 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.949 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:32.949 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:32.950 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:32.953 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:32.953 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:32.954 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:32.954 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:32.954 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.583 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:10:44.590 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.591 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:44.591 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.593 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:44.596 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:44.597 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:44.597 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:44.598 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:44.598 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.605 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:10:44.608 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.608 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:44.609 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.609 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:44.612 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:44.613 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:44.613 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:44.614 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:44.614 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:44.688 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:10:44.689 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:10:44.696 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.699 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:44.700 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.700 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:44.704 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:44.704 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.705 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:44.706 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:44.706 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:44.707 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:44.707 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.708 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.708 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:44.713 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:44.714 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:44.715 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:44.715 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:44.716 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.811 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:10:44.816 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.817 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:10:44.817 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:10:44.817 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:10:44.821 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:10:44.822 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:10:44.822 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:10:44.823 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:10:44.823 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:11:32.226 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - === 获取当前用户信息接口开始 ===
2025-07-30 10:11:32.233 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 接收到的用户名: admin
2025-07-30 10:11:32.234 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 接收到的用户ID: null
2025-07-30 10:11:32.234 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - === 所有请求头信息 ===
2025-07-30 10:11:32.234 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: accept-encoding = gzip
2025-07-30 10:11:32.234 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: sec-ch-ua-platform = "Windows"
2025-07-30 10:11:32.234 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: accept-language = zh-CN,zh;q=0.9
2025-07-30 10:11:32.235 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: accept = application/json, text/plain, */*
2025-07-30 10:11:32.235 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: sec-ch-ua = "Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"
2025-07-30 10:11:32.241 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36
2025-07-30 10:11:32.241 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: token = eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc1MzkyNzg5MiwiaWF0IjoxNzUzODQxNDkyfQ.inszMRboAclJ4ec_rKsYV__2duVrDOCbbjuO9rwsuZM
2025-07-30 10:11:32.242 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: sec-ch-ua-mobile = ?0
2025-07-30 10:11:32.242 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: origin = http://localhost:3050
2025-07-30 10:11:32.243 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: sec-fetch-site = same-site
2025-07-30 10:11:32.243 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: sec-fetch-mode = cors
2025-07-30 10:11:32.243 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: sec-fetch-dest = empty
2025-07-30 10:11:32.244 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: referer = http://localhost:3050/
2025-07-30 10:11:32.244 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: cookie = token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc1MzkyNzg5MiwiaWF0IjoxNzUzODQxNDkyfQ.inszMRboAclJ4ec_rKsYV__2duVrDOCbbjuO9rwsuZM; admin_token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc1MzkyNzg5MiwiaWF0IjoxNzUzODQxNDkyfQ.inszMRboAclJ4ec_rKsYV__2duVrDOCbbjuO9rwsuZM
2025-07-30 10:11:32.245 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: x-user-name = admin
2025-07-30 10:11:32.245 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: x-route-name = service-user
2025-07-30 10:11:32.246 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: forwarded = proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:51247"
2025-07-30 10:11:32.246 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-for = 0:0:0:0:0:0:0:1
2025-07-30 10:11:32.248 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-proto = http
2025-07-30 10:11:32.249 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-prefix = /gateway
2025-07-30 10:11:32.249 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-port = 8090
2025-07-30 10:11:32.250 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: x-forwarded-host = localhost:8090
2025-07-30 10:11:32.250 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: host = localhost:8090
2025-07-30 10:11:32.251 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 请求头: content-length = 0
2025-07-30 10:11:32.251 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - === 请求头信息结束 ===
2025-07-30 10:11:32.251 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 开始通过用户名查找用户: admin
2025-07-30 10:11:32.252 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 首先在系统用户表中查找用户: admin
2025-07-30 10:11:32.256 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 在系统用户表中找到用户: ID=1, 用户名=admin, 真实姓名=系统管理员
2025-07-30 10:11:32.256 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 设置admin系统用户角色和权限
2025-07-30 10:11:32.257 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 构建的系统用户信息: {roles=[admin], avatar=null, enabled=true, realName=系统管理员, lastLoginTime=null, balance=0.0, phone=13800138000, createTime=null, permissions=[*], name=系统管理员, id=1, userType=system, email=<EMAIL>, username=admin, status=active}
2025-07-30 10:11:32.258 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - === 获取当前系统用户信息成功 ===
2025-07-30 10:12:01.136 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:01.140 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.140 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:01.141 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.142 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:01.145 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:01.146 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:01.146 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:01.146 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:01.146 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.153 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:01.156 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.156 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:01.156 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.157 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:01.161 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:01.162 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:01.162 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:01.163 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:01.163 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:01.188 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:01.200 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:01.203 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.203 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:01.205 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.205 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:01.214 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.215 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:01.216 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:01.217 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:01.219 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:01.220 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:01.220 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.219 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.221 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:01.230 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:01.233 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:01.235 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:01.236 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:01.236 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.298 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:01.302 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.302 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:01.303 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:01.303 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:01.306 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:01.307 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:01.307 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:01.307 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:01.308 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.035 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:10.039 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.040 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:10.040 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.040 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:10.043 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:10.044 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:10.044 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:10.045 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:10.046 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.052 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:10.056 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.056 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:10.056 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.073 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:10.076 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:10.076 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:10.077 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:10.078 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:10.078 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:10.114 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:10.118 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:10.120 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.121 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:10.122 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.122 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.123 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:10.123 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:10.124 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.125 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:10.127 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:10.127 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:10.127 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:10.129 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:10.129 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:10.129 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:10.129 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.130 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:10.131 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:10.131 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.223 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:10.226 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.227 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:10.227 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:10.227 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:10.230 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:10.230 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:10.231 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:10.231 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:10.231 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:12.521 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:12.532 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:12.533 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:12.533 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:12.534 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:12.538 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:12.538 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:12.538 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:12.539 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:12.539 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:12.544 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:12.547 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:12.547 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:12.547 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:12.548 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:12.550 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:12.550 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:12.551 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:12.551 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:12.552 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:12.567 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:12.572 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:12.573 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:12.573 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:12.574 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:12.586 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:12.586 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:12.587 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:12.587 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:12.588 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:12.607 [http-nio-8082-exec-5] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:12:19.409 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:19.414 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:19.414 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:19.415 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:19.415 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:19.417 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:19.418 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:19.418 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:19.419 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:19.419 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:19.431 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:12:19.457 [http-nio-8082-exec-4] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:12:19.469 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:12:19.491 [http-nio-8082-exec-5] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 10:12:40.763 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:40.855 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.865 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:40.866 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.867 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:40.871 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:40.871 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:40.871 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:40.872 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:40.872 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.879 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:40.883 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.883 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:40.884 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.884 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:40.888 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:40.888 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:40.889 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:40.889 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:40.890 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:40.912 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:40.917 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:40.918 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.919 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:40.919 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.920 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:40.922 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.923 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:40.923 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:40.923 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.924 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:40.924 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:40.924 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:40.925 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:40.925 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:40.931 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:40.932 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:40.933 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:40.933 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:40.934 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:41.005 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:12:41.019 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:41.052 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:41.053 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:41.053 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:41.057 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:41.057 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:41.058 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:41.058 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:41.058 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:41.773 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:41.778 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:41.779 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:41.779 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:41.780 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:41.783 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:41.784 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:41.784 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:41.784 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:41.785 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:41.790 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 776a98d6-c78d-488b-9fd6-3c5790f013e3
2025-07-30 10:12:41.793 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:41.793 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:12:41.794 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:12:41.794 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:12:41.798 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:12:41.798 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:12:41.798 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:12:41.799 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:12:41.799 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:02.768 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:02.773 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.773 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:02.774 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.774 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:02.778 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:02.778 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:02.778 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:02.779 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:02.779 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.785 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:02.789 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.789 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:02.790 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.790 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:02.793 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:02.794 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:02.794 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:02.795 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:02.795 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:02.858 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:02.860 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:02.866 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.867 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:02.869 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.869 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:02.873 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.874 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:02.874 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:02.875 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:02.875 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.876 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:02.876 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:02.877 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:02.877 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.886 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:02.886 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:02.887 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:02.892 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:02.893 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.964 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:02.968 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.969 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:02.969 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:02.969 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:02.972 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:02.972 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:02.973 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:02.973 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:02.973 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.278 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:09.283 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.283 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:09.284 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.284 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:09.287 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:09.287 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:09.288 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:09.288 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:09.289 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.294 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:09.297 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.297 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:09.297 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.298 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:09.300 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:09.301 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:09.301 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:09.301 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:09.302 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:09.348 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:09.353 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.354 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:09.356 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.357 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:09.356 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:09.361 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:09.362 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:09.362 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.363 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:09.363 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:09.363 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:09.364 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.364 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.365 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:09.370 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:09.370 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:09.373 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:09.375 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:09.375 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.426 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 10:13:09.433 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.433 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 10:13:09.434 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 10:13:09.434 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 10:13:09.437 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 10:13:09.437 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 10:13:09.438 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 10:13:09.438 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 10:13:09.438 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.501 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:04:53.603 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.604 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:04:53.605 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.605 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:04:53.617 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:04:53.618 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:04:53.618 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:04:53.620 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:04:53.621 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.655 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:04:53.669 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.669 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:04:53.670 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.670 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:04:53.675 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:04:53.675 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:04:53.676 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:04:53.676 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:04:53.677 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:04:53.700 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:04:53.702 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:04:53.705 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.706 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:04:53.706 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.710 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:04:53.713 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.714 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:04:53.714 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:04:53.715 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.716 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:04:53.716 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:04:53.717 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:04:53.719 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:04:53.720 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.722 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:04:53.722 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:04:53.723 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:04:53.724 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:04:53.724 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.839 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:04:53.849 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.849 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:04:53.850 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:04:53.851 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:04:53.855 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:04:53.855 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:04:53.855 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:04:53.856 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:04:53.857 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.510 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:05:26.516 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.516 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:26.516 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.517 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:26.522 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:26.522 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:26.523 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:26.523 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:26.523 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.529 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:05:26.533 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.533 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:26.534 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.534 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:26.536 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:26.537 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:26.537 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:26.537 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:26.538 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:26.577 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:05:26.582 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.583 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:26.584 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.583 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:05:26.584 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:26.589 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.591 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:26.591 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.592 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:26.591 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:26.593 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:26.594 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:26.594 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:26.595 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.604 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:26.604 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:26.606 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:26.606 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:26.607 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.634 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: 07e1dd0c-0b95-440b-8452-5f23583430df
2025-07-30 11:05:26.638 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.639 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:26.639 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:26.640 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:26.643 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:26.643 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:26.643 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:26.644 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:26.644 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:46.912 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 接收到微信小程序登录请求，code: 0e3eIj100oJYHU1Cpw100BldTN2eIj1T
2025-07-30 11:05:46.913 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 调用微信接口: https://api.weixin.qq.com/sns/jscode2session?appid=wx9102c8b1b86d0671&secret=cd6b00a317ce270ed704dfdd6fc8c209&js_code=0e3eIj100oJYHU1Cpw100BldTN2eIj1T&grant_type=authorization_code
2025-07-30 11:05:47.766 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 微信接口返回原始响应: {"session_key":"HFYTZ86k9cBnK07WCRCF8w==","openid":"oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4"}
2025-07-30 11:05:47.773 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 解析后的微信响应: {session_key=HFYTZ86k9cBnK07WCRCF8w==, openid=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4}
2025-07-30 11:05:47.773 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取到用户openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:47.774 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:47.774 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:47.775 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:47.778 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:47.778 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:47.779 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:47.779 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:47.780 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 更新用户信息: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:47.975 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 用户已存在，更新登录时间: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.010 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.TokenServiceImpl - 创建新token: faeabdbf-8da6-4399-8c14-6b5543a4647c for user: 46, openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.011 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 创建token成功: faeabdbf-8da6-4399-8c14-6b5543a4647c for user: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.077 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:48.078 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:48.082 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.082 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:48.082 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.083 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.083 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:48.083 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:48.084 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.085 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:48.087 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:48.087 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:48.087 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:48.088 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:48.088 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:48.088 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:48.089 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:48.089 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.089 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:48.090 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.120 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:48.123 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:48.132 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.135 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:48.136 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.137 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:48.141 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.143 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:48.144 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:48.143 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:48.145 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:48.145 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.146 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:48.147 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:48.147 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.153 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:48.155 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:48.156 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:48.156 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:48.157 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.203 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:48.203 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:48.207 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.208 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.208 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:48.209 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.210 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:48.208 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:48.212 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.212 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:48.215 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:48.216 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:48.216 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:48.216 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:48.217 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:48.217 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:48.217 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:48.218 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.218 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:48.219 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.248 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:48.251 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.252 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:48.252 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.252 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:48.255 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:48.255 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:48.255 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:48.256 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:48.256 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.295 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:48.298 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.298 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:48.298 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.299 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:48.301 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:48.301 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:48.302 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:48.302 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:48.302 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.314 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:48.317 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.317 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:48.318 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:48.318 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:48.320 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:48.320 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:48.321 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:48.321 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:48.321 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:50.700 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:50.703 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:50.704 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:50.704 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:50.704 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:50.707 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:50.707 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:50.707 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:50.708 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:50.708 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:50.712 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:50.717 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:50.718 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:50.718 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:50.719 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:50.721 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:50.722 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:50.722 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:50.722 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:50.722 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:50.742 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:50.746 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:50.747 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:50.748 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:50.749 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:50.752 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:50.752 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:50.753 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:50.753 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:50.753 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:50.773 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:05:55.027 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:05:55.030 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:55.030 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:05:55.031 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:55.031 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:05:55.034 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:05:55.034 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:05:55.034 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:05:55.035 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:05:55.035 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:05:55.053 [http-nio-8082-exec-1] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:05:55.148 [http-nio-8082-exec-4] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:05:55.166 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:05:55.220 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:06:14.048 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:06:14.052 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:06:14.052 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:06:14.053 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:06:14.053 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:06:14.057 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:06:14.057 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:06:14.058 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:06:14.058 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:06:14.058 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:06:14.062 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:06:14.065 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:06:14.066 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:06:14.066 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:06:14.067 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:06:14.070 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:06:14.070 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:06:14.071 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:06:14.071 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:06:14.071 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:30.305 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:30.390 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.391 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:30.391 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.392 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:30.402 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:30.403 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:30.403 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:30.404 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:30.404 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.414 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:30.417 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.417 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:30.418 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.419 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:30.422 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:30.422 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:30.422 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:30.423 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:30.423 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:30.466 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:30.472 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:30.472 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.473 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:30.476 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.477 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:30.479 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.480 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:30.480 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:30.480 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.481 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:30.481 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:30.482 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:30.483 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:30.483 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.486 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:30.486 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:30.489 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:30.490 [http-nio-8082-exec-2] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:30.490 [http-nio-8082-exec-2] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.717 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:30.723 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.723 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:30.724 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:30.725 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:30.732 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:30.733 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:30.733 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:30.733 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:30.734 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:33.526 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:33.534 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:33.535 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:33.535 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:33.536 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:33.541 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:33.542 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:33.542 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:33.543 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:33.546 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:33.554 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:33.562 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:33.563 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:33.563 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:33.564 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:33.567 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:33.568 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:33.568 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:33.569 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:33.569 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:33.597 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:33.603 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:33.603 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:33.604 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:33.604 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:33.608 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:33.609 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:33.609 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:33.610 [http-nio-8082-exec-3] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:33.611 [http-nio-8082-exec-3] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:33.684 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:40:38.024 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:38.034 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:38.034 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:38.035 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:38.035 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:38.038 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:38.039 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:38.040 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:38.040 [http-nio-8082-exec-1] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:38.041 [http-nio-8082-exec-1] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:38.093 [http-nio-8082-exec-4] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:40:38.438 [http-nio-8082-exec-5] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:40:38.454 [http-nio-8082-exec-3] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:40:38.564 [http-nio-8082-exec-2] INFO  c.e.user.controller.UserController - 根据ID获取用户: 46
2025-07-30 11:40:56.443 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 验证token: faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:56.454 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:56.455 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:56.455 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:56.455 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:56.458 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:56.458 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:56.458 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:56.458 [http-nio-8082-exec-4] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:56.459 [http-nio-8082-exec-4] INFO  c.e.u.controller.MiniAppController - token验证成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:56.462 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息，token: Bearer faeabdbf-8da6-4399-8c14-6b5543a4647c
2025-07-30 11:40:56.465 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 从token获取到openid: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:56.465 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 开始 ===
2025-07-30 11:40:56.467 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 查询用户名: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4
2025-07-30 11:40:56.467 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 开始执行数据库查询...
2025-07-30 11:40:56.470 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 数据库查询完成，结果: 找到用户
2025-07-30 11:40:56.470 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 成功找到用户: ID=46, 用户名=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 全名=云端车牌识别～沈泽颖, 邮箱=null, 启用状态=true
2025-07-30 11:40:56.471 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - 用户详细信息: 创建时间=2025-06-17T10:58:01.015771, 余额=90.38, 头像=/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
2025-07-30 11:40:56.471 [http-nio-8082-exec-5] INFO  c.e.u.service.impl.UserServiceImpl - === UserService.findByUsername 结束 ===
2025-07-30 11:40:56.471 [http-nio-8082-exec-5] INFO  c.e.u.controller.MiniAppController - 获取用户信息成功，用户: oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 头像: https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg
