package com.ebcp.charger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.core.env.Environment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.IntegrationComponentScan;
import java.net.InetAddress;
import java.net.UnknownHostException;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 充电桩服务启动类
 */
@SpringBootApplication(exclude = {
    JpaRepositoriesAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class
})
@EnableDiscoveryClient
@EnableWebMvc
@RefreshScope
@MapperScan("com.ebcp.charger.mapper")
@IntegrationComponentScan
@EnableScheduling
@EnableAsync
public class ChargerServiceApplication {

    private static final Logger logger = LoggerFactory.getLogger(ChargerServiceApplication.class);
    
    @Autowired
    private Environment env;

    public static void main(String[] args) {
        logger.info("启动充电桩服务...");
        // 设置属性以确保配置中心客户端正确初始化
        System.setProperty("spring.cloud.bootstrap.enabled", "true");
        SpringApplication app = new SpringApplication(ChargerServiceApplication.class);
        app.setLogStartupInfo(false);
        app.run(args);
    }
    
    @Bean
    public ApplicationListener<ApplicationReadyEvent> readyEventApplicationListener() {
        return event -> {
            logger.info("========================================================");
            logger.info("充电桩服务启动完成，路径映射:");
            logger.info("/stations/** -> StationController");
            logger.info("/chargers/** -> ChargerController");
            
            // 添加额外的启动信息
            try {
                String hostAddress = InetAddress.getLocalHost().getHostAddress();
                String port = env.getProperty("server.port");
                logger.info("服务地址: http://{}:{}", hostAddress, port);
                logger.info("应用名称: {}", env.getProperty("spring.application.name"));
                
                // 打印安全配置信息
                logger.info("安全配置信息:");
                logger.info("- Spring Security自动配置是否启用: {}", 
                    !env.getProperty("spring.autoconfigure.exclude", "").contains("SecurityAutoConfiguration"));
                
                // 检查是否存在任何JWT相关的配置
                logger.info("- JWT配置: {}", env.getProperty("jwt.secret") != null ? "已配置" : "未配置");
                
                // 检查过滤器配置
                logger.info("- 注册的过滤器: LoggingFilter (优先级: 最高)");
                
                // 检查MQTT配置
                logger.info("- MQTT服务器: {}", env.getProperty("mqtt.broker.url"));
                logger.info("- MQTT主题: {}", env.getProperty("mqtt.topics"));
                logger.info("- client-id: {}", env.getProperty("mqtt.broker.client-id"));
            } catch (UnknownHostException e) {
                logger.warn("无法确定主机地址: {}", e.getMessage());
            }
            
            logger.info("========================================================");
        };
    }
} 