package com.ebcp.charger.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebcp.charger.dto.*;
import com.ebcp.charger.entity.Charger;
import com.ebcp.charger.entity.ChargingOrder;
import com.ebcp.charger.entity.Station;
import com.ebcp.charger.mapper.ChargerMapper;
import com.ebcp.charger.mapper.ChargingOrderMapper;
import com.ebcp.charger.mapper.StationMapper;
import com.ebcp.charger.service.*;
import com.ebcp.charger.config.WechatConfig;
import com.ebcp.charger.util.ImageUtils;
import com.ebcp.common.dto.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微信小程序专用接口控制器
 * 这些接口不需要JWT认证即可访问
 */
@RestController
@RequestMapping("/mini")
@Slf4j
public class MiniAppController {

    @Autowired
    private ChargerService chargerService;
    
    // 添加ChargingService依赖
    @Autowired(required = false)
    private ChargingService chargingService;
    
    @Autowired
    private ChargingOrderService chargingOrderService;
    
    @Autowired
    private WechatMiniProgramService wechatMiniProgramService;
    
    @Autowired
    private WechatConfig wechatConfig;
    
    @Autowired
    private StationMapper stationMapper;
    
    @Autowired(required = false)
    private com.ebcp.charger.service.ChargingFeeSettlementService chargingFeeSettlementService;
    
    @Autowired(required = false)
    private WechatNotificationService wechatNotificationService;

    // 🔧 新增：添加缺少的依赖注入
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // 🔧 新增：退款服务客户端
    @Autowired(required = false)
    private com.ebcp.charger.client.RefundServiceClient refundServiceClient;
    
    /**
     * 获取充电桩详情（小程序专用，不需要认证）
     * 此接口直接访问ChargerService获取数据，跳过认证
     */
    @GetMapping("/charger/detail")
    public ResponseDTO<ChargerDTO> getChargerDetail(
            @RequestParam("id") String chargerId,
            @RequestParam("socketId") String socketId,
            @RequestParam(value = "scene", required = false) String scene) {
        
        log.info("微信小程序获取充电桩详情: id={}, socketId={}, scene={}", chargerId, socketId, scene);
        
        try {
            // 检查参数
            if (StringUtils.isEmpty(chargerId)) {
                return ResponseDTO.error("充电桩ID不能为空");
            }
            
            Long chargerIdLong = null;
            Integer socketIdInt = null;
            
            try {
                chargerIdLong = Long.parseLong(chargerId);
                socketIdInt = Integer.parseInt(socketId);
            } catch (NumberFormatException e) {
                return ResponseDTO.error("参数格式不正确");
            }
            
            // 查询充电桩
            Charger charger = chargerService.getById(chargerIdLong);
            if (charger == null) {
                return ResponseDTO.error("充电桩不存在");
            }
            
            // 将实体转换为DTO
            ChargerDTO chargerDTO = ChargerDTO.builder()
                    .id(charger.getId())
                    .code(charger.getCode())
                    .serialNumber(charger.getSerialNumber())
                    .name(charger.getName())
                    .type(charger.getType())
                    .status(charger.getStatus())
                    .stationId(charger.getStationId())
                    .stationName(getStationName(charger.getStationId()))
                    .address(charger.getAddress())
                    .latitude(charger.getLatitude())
                    .longitude(charger.getLongitude())
                    .sockets(charger.getSockets())
                    .online(charger.getOnline())
                    .socketCount(charger.getSockets())
                    .build();
            
            // 设置充电桩类型特定信息
            setChargerTypeInfo(chargerDTO, charger);
            
            // 查询当前插座是否有活跃的充电订单（包括已创建和充电中）
            ChargingOrder activeOrder = chargingOrderService.getOne(
                    new QueryWrapper<ChargingOrder>()
                            .eq("charger_id", chargerIdLong)
                            .eq("socket", socketIdInt)
                            .in("status", 0, 1)  // 状态0表示已创建未充电，1表示充电中
                            .orderByDesc("start_time")
                            .last("LIMIT 1")
            );
            
            // 设置小程序相关状态
            Map<String, Object> miniAppInfo = new HashMap<>();
            miniAppInfo.put("socket", socketIdInt);
            
            if (activeOrder != null) {
                if (activeOrder.getStatus() == 1) {
                    // 状态为1表示充电中
                    miniAppInfo.put("status", "charging");
                } else {
                    // 状态为0表示已创建但未开始充电
                    miniAppInfo.put("status", "pending");
                }
                
                miniAppInfo.put("orderId", activeOrder.getOrderId());
                miniAppInfo.put("startTime", activeOrder.getStartTime());
                miniAppInfo.put("orderStatus", activeOrder.getStatus()); // 添加原始订单状态
                
                // 计算已充电时间（分钟）
                long chargedMinutes = 0;
                if (activeOrder.getStartTime() != null) {
                    chargedMinutes = java.time.Duration.between(
                            activeOrder.getStartTime(), 
                            LocalDateTime.now()
                    ).toMinutes();
                }
                
                miniAppInfo.put("chargedMinutes", chargedMinutes);
                miniAppInfo.put("estimatedTime", activeOrder.getEstimatedTime());
            } else {
                // 当前插座无活跃订单
                miniAppInfo.put("status", "idle");
            }
            
            chargerDTO.setMiniAppInfo(miniAppInfo);
            
            // 构建插座状态列表
            List<Integer> socketStatusList = new ArrayList<>();
            
            // 优先使用设备返回的原始状态数据
            if (charger.getSocketStatusList() != null && !charger.getSocketStatusList().isEmpty()) {
                // 解析设备返回的状态数据
                try {
                    String statusListStr = charger.getSocketStatusList();
                    // 去除两端的方括号并按逗号分割
                    String[] statusArray = statusListStr.replace("[", "").replace("]", "").split(",");
                    
                    // 转换为整数列表
                    for (String status : statusArray) {
                        socketStatusList.add(Integer.parseInt(status.trim()));
                    }
                    
                    log.info("使用设备原始端口状态: {}", socketStatusList);
                } catch (Exception e) {
                    log.error("解析设备原始状态数据失败", e);
                    // 解析失败时创建默认的状态列表
                    socketStatusList.clear();
                    createDefaultSocketStatusList(charger, socketStatusList, socketIdInt, activeOrder);
                }
            } else {
                // 没有设备原始状态数据，创建默认的状态列表
                createDefaultSocketStatusList(charger, socketStatusList, socketIdInt, activeOrder);
            }
            
            chargerDTO.setSocketStatusList(socketStatusList);
            
            // 打印充电桩端口状态信息
            log.info("充电桩 ID={} 的端口状态列表: {}", chargerId, socketStatusList);
            log.info("查询端口 socketId={} 的订单状态: {}", socketId, activeOrder != null ? 
                (activeOrder.getStatus() == 1 ? "充电中(订单ID:" + activeOrder.getOrderId() + ")" : 
                "待充电(订单ID:" + activeOrder.getOrderId() + ")") : "空闲");
            if (charger.getSocketStatusList() != null) {
                log.info("充电桩原始设备返回的端口状态数据: {}", charger.getSocketStatusList());
            }
            
            return ResponseDTO.success(chargerDTO);
        } catch (Exception e) {
            log.error("获取充电桩详情失败", e);
            return ResponseDTO.error("获取充电桩详情失败: " + e.getMessage());
        }
    }


    /**
     * 微信小程序-开始充电
     */
    @PostMapping("/charger/start-charging")
    public ResponseDTO<Map<String, Object>> startCharging(@RequestBody Map<String, Object> params,
                                                         HttpServletRequest httpRequest) {
        log.info("微信小程序-开始充电请求参数: {}", params);
        
        try {
            // 获取参数
            Long chargerId = Long.parseLong(params.get("chargerId").toString());
            Integer socket = Integer.parseInt(params.get("socket").toString());
            Integer time = Integer.parseInt(params.get("time").toString());
            
            // 🔧 优先从请求头获取用户ID（网关注入的）
            Long userId = null;
            String userIdHeader = httpRequest.getHeader("X-User-ID");
            if (userIdHeader != null && !userIdHeader.trim().isEmpty()) {
                try {
                    userId = Long.parseLong(userIdHeader);
                    log.info("从请求头获取到用户ID: {}", userId);
                } catch (NumberFormatException e) {
                    log.warn("请求头中用户ID格式不正确: {}", userIdHeader);
                }
            }
            
            // 如果请求头中没有，则从请求体中获取（备选方案）
            if (userId == null && params.containsKey("userId") && params.get("userId") != null) {
                try {
                    userId = Long.parseLong(params.get("userId").toString());
                    log.info("从请求体获取到用户ID: {}", userId);
                } catch (NumberFormatException e) {
                    log.warn("请求体中用户ID参数格式不正确: {}", params.get("userId"));
                }
            }
            
            // 验证用户ID
            if (userId == null) {
                log.error("无法获取用户ID，无法开始充电");
                return ResponseDTO.error("用户未登录或用户信息异常");
            }
            
            // 🔧 新增：获取用户实际支付金额
            Double paidAmount = null;
            if (params.containsKey("paidAmount") && params.get("paidAmount") != null) {
                try {
                    paidAmount = Double.parseDouble(params.get("paidAmount").toString());
                    log.info("接收到用户实际支付金额: {}元", paidAmount);
                } catch (NumberFormatException e) {
                    log.warn("支付金额参数格式不正确: {}", params.get("paidAmount"));
                }
            }
            
            // 🔧 新增：获取充电方案描述
            String planDescription = null;
            if (params.containsKey("planDescription") && params.get("planDescription") != null) {
                planDescription = params.get("planDescription").toString().trim();
                log.info("接收到充电方案描述: {}", planDescription);
            }
            
            // 可选参数
            Integer minPower = params.containsKey("minPower") ? Integer.parseInt(params.get("minPower").toString()) : 500;
            Integer maxPower = params.containsKey("maxPower") ? Integer.parseInt(params.get("maxPower").toString()) : 2000;
            Integer chargeType = params.containsKey("chargeType") ? Integer.parseInt(params.get("chargeType").toString()) : 1;
            Integer paymentType = params.containsKey("paymentType") ? Integer.parseInt(params.get("paymentType").toString()) : 0;
            
            // 获取余额参数，用于传递给设备
            Double balance = null;
            if (params.containsKey("balance") && params.get("balance") != null) {
                try {
                    // 🔧 修复：使用BigDecimal处理余额精度问题
                    BigDecimal balanceBD = new BigDecimal(params.get("balance").toString())
                                            .setScale(2, RoundingMode.HALF_UP);
                    balance = balanceBD.doubleValue();
                    log.info("接收到用户预充值金额（传给充电桩）: {}元", balance);
                } catch (NumberFormatException e) {
                    log.warn("预充值金额参数格式不正确: {}", params.get("balance"));
                }
            }
            
            // 验证基本参数
            if (chargerId == null || socket == null || time == null) {
                return ResponseDTO.error("参数不完整，请确保提供充电桩ID、端口和充电时间");
            }
            
            // 构建启动充电参数
            StartChargingRequest request = new StartChargingRequest();
            request.setChargerId(chargerId);
            request.setSocket(socket);
            request.setMinPower(minPower);
            request.setMaxPower(maxPower);
            request.setIdleTimeout(300); // 默认5分钟空载超时
            request.setFullTimeout(300); // 默认5分钟充满超时
            request.setTime(time);
            request.setChargeType(chargeType);
            request.setPaymentType(paymentType);
            // 🔧 设置用户ID
            request.setUserId(userId);
            log.info("设置充电用户ID: {}", userId);
            
            // 设置余额参数
            if (balance != null) {
                request.setBalance(balance);
                log.info("设置用户预充值金额（传给充电桩）: {}元", balance);
            }
            
            // 🔧 设置支付金额参数
            if (paidAmount != null) {
                request.setPaidAmount(paidAmount);
                log.info("设置用户实际支付金额: {}元", paidAmount);
            }
            
            // 🔧 设置充电方案描述
            if (planDescription != null && !planDescription.isEmpty()) {
                request.setPlanDescription(planDescription);
                log.info("设置充电方案描述: {}", planDescription);
            }
            
            // 调用ChargingService服务
                if (chargingService != null) {
                try {
                    StartChargingResponse response = chargingService.startCharging(request);
                    
                    // 构造响应结果
                    Map<String, Object> result = new HashMap<>();
                    result.put("orderId", response.getOrderId());
                    result.put("result", response.getResult());
                    result.put("status", response.getStatus());
                    result.put("timestamp", response.getTimestamp());
                    
                    ResponseDTO<Map<String, Object>> responseDTO = ResponseDTO.success(result);
                    
                    if (response.getResult() == 1) {
                        // 充电成功启动
                        responseDTO.setMessage("开启充电成功");
                    } else if (response.getResult() == -1) {
                        // 指令已发送，等待设备响应
                        responseDTO.setMessage("充电指令已发送，等待设备响应");
                    } else {
                        // 🔧 充电失败 - 立即触发退款
                        String statusDesc = response.getStatusDesc();
                        if (statusDesc == null || statusDesc.isEmpty()) {
                            statusDesc = "未知状态";
                        }

                        // 🔧 关键修复：充电启动失败时立即退款
                        String orderId = response.getOrderId();
                        if (orderId != null && refundServiceClient != null) {
                            try {
                                log.info("🔧 充电启动失败，开始处理退款：订单ID={}, 失败原因={}", orderId, statusDesc);

                                // 直接调用退款服务
                                com.ebcp.common.dto.ResponseDTO<Object> refundResult = refundServiceClient.processChargingFailureRefund(
                                    orderId, "充电启动失败：" + statusDesc);

                                if (refundResult != null && refundResult.getCode() == 200) {
                                    log.info("✅ 充电启动失败退款成功：订单ID={}", orderId);
                                } else {
                                    log.error("❌ 充电启动失败退款失败：订单ID={}, 错误={}", orderId,
                                            refundResult != null ? refundResult.getMessage() : "退款服务异常");
                                }
                            } catch (Exception refundException) {
                                log.error("❌ 调用退款服务异常：订单ID={}", orderId, refundException);
                            }
                        }

                        responseDTO = ResponseDTO.error("开启充电失败: " + statusDesc + "，支付金额将自动退回");
                    }
                    
                    log.info("小程序充电启动结果: {}, 消息: {}", result, responseDTO.getMessage());
                    return responseDTO;
                } catch (Exception e) {
                    log.error("调用充电服务异常", e);
                    // 发生异常，降级使用ChargerService
                }
            }
            
            // 降级方案：使用ChargerService
                Map<String, Object> startParams = new HashMap<>();
                startParams.put("chargerId", chargerId);
                startParams.put("socket", socket);
                startParams.put("minPower", minPower);
                startParams.put("maxPower", maxPower);
                startParams.put("idleTimeout", 300);
                startParams.put("fullTimeout", 300);
                startParams.put("time", time);
                startParams.put("chargeType", chargeType);
                startParams.put("paymentType", paymentType);
                
                // 降级方案中也传递余额参数
                if (balance != null) {
                    startParams.put("balance", balance);
                }
                
                Map<String, Object> result = chargerService.startCharging(startParams);
                log.info("小程序充电启动结果(降级服务): {}", result);

                ResponseDTO<Map<String, Object>> responseDTO = ResponseDTO.success(result);
                if (result != null && result.containsKey("orderId")) {
                    responseDTO.setMessage("充电指令已发送，等待设备响应");
                } else {
                    // 🔧 降级方案中的充电失败 - 也需要退款
                    String errorMessage = result != null ? result.get("message").toString() : "未知错误";

                    // 🔧 降级方案充电失败 - 直接通过用户ID和充电桩信息进行退款
                    if (refundServiceClient != null) {
                        try {
                            log.info("🔧 降级方案充电失败，开始处理退款：用户ID={}, 充电桩ID={}, 错误={}", userId, chargerId, errorMessage);

                            // 🔧 方法1：查找该用户在该充电桩的最近订单
                            ChargingOrder recentOrder = findRecentChargingOrder(userId, chargerId, socket);
                            if (recentOrder != null) {
                                log.info("🔧 找到最近订单，开始退款：订单ID={}, 状态={}", recentOrder.getOrderId(), recentOrder.getStatus());

                                // 直接调用退款，不延迟
                                com.ebcp.common.dto.ResponseDTO<Object> refundResult = refundServiceClient.processChargingFailureRefund(
                                    recentOrder.getOrderId(), "充电启动失败(降级)：" + errorMessage);

                                if (refundResult != null && refundResult.getCode() == 200) {
                                    log.info("✅ 降级方案退款成功：订单ID={}", recentOrder.getOrderId());
                                } else {
                                    log.error("❌ 降级方案退款失败：订单ID={}, 错误={}", recentOrder.getOrderId(),
                                            refundResult != null ? refundResult.getMessage() : "退款服务异常");
                                }
                            } else {
                                // 🔧 方法2：如果找不到订单，尝试通过用户最近的支付记录退款
                                log.warn("⚠️ 未找到最近订单，尝试通过用户最近支付记录退款：用户ID={}", userId);
                                processRecentPaymentRefund(userId, "充电启动失败(降级)：" + errorMessage);
                            }
                        } catch (Exception refundException) {
                            log.error("❌ 降级方案退款异常：用户ID={}, 充电桩ID={}", userId, chargerId, refundException);
                        }
                    }

                    responseDTO.setMessage("启动充电失败: " + errorMessage + "，支付金额将自动退回");
                    responseDTO.setCode(500);
                }

                return responseDTO;
        } catch (Exception e) {
            log.error("微信小程序-开始充电异常", e);
            return ResponseDTO.error("启动充电异常: " + e.getMessage());
        }
    }

    /**
     * 微信小程序-停止充电
     */
    @PostMapping("/charger/stop-charging")
    public ResponseDTO<Map<String, Object>> stopCharging(@RequestBody Map<String, Object> params) {
        log.info("微信小程序-停止充电请求参数: {}", params);
        
        try {
            // 获取订单ID
            String orderId = null;
            if (params.get("orderId") != null && !params.get("orderId").toString().trim().isEmpty()) {
                orderId = params.get("orderId").toString().trim();
            }
            
            if (orderId == null || orderId.isEmpty()) {
                return ResponseDTO.error("订单ID不能为空");
            }
            
            // 🔧 修复：优先从订单信息中获取充电桩ID和端口信息
            ChargingOrder order = chargingOrderService.getOrderByOrderId(orderId);
            if (order == null) {
                // 🔧 新增：如果通过订单ID找不到，尝试解析特殊格式的订单ID
                // 格式可能是：充电桩ID_端口号_时间戳（如：13_1_1749455043836）
                log.info("通过订单ID未找到订单，尝试解析特殊格式：{}", orderId);
                
                Long parsedChargerId = null;
                Integer parsedSocket = null;
                
                if (orderId.contains("_")) {
                    String[] parts = orderId.split("_");
                    if (parts.length >= 2) {
                        try {
                            parsedChargerId = Long.parseLong(parts[0]);
                            parsedSocket = Integer.parseInt(parts[1]);
                            log.info("解析特殊格式订单ID：充电桩ID={}, 端口={}", parsedChargerId, parsedSocket);
                        } catch (NumberFormatException e) {
                            log.warn("无法解析订单ID格式：{}", orderId);
                        }
                    }
                }
                
                // 如果解析成功，查找该充电桩端口的正在充电订单
                if (parsedChargerId != null && parsedSocket != null) {
                    order = chargingOrderService.getOne(
                        new QueryWrapper<ChargingOrder>()
                            .eq("charger_id", parsedChargerId)
                            .eq("socket", parsedSocket)
                            .eq("status", 1) // 充电中
                            .orderByDesc("start_time")
                            .last("LIMIT 1")
                    );
                    
                    if (order != null) {
                        log.info("通过充电桩ID和端口找到正在充电的订单：{}", order.getOrderId());
                        // 更新orderId为实际的订单ID，后续逻辑使用正确的订单ID
                        orderId = order.getOrderId();
                    }
                }
                
                // 如果还是没找到，最后尝试通过请求参数中的充电桩ID和端口查找
                if (order == null && params.get("stationId") != null && params.get("socketId") != null) {
                    try {
                        Long requestChargerId = Long.parseLong(params.get("stationId").toString());
                        Integer requestSocket = Integer.parseInt(params.get("socketId").toString());
                        
                        order = chargingOrderService.getOne(
                            new QueryWrapper<ChargingOrder>()
                                .eq("charger_id", requestChargerId)
                                .eq("socket", requestSocket)
                                .eq("status", 1) // 充电中
                                .orderByDesc("start_time")
                                .last("LIMIT 1")
                        );
                        
                        if (order != null) {
                            log.info("通过请求参数找到正在充电的订单：{}", order.getOrderId());
                            orderId = order.getOrderId();
                        }
                    } catch (Exception e) {
                        log.warn("通过请求参数查找订单失败：{}", e.getMessage());
                    }
                }
                
                // 如果所有方法都没找到订单
                if (order == null) {
                    return ResponseDTO.error("未找到对应的充电订单: " + orderId);
                }
            }
            
            Long chargerId = order.getChargerId();
            Integer socket = order.getSocket();
            
            // 🔧 如果订单中没有必要信息，尝试从请求参数中获取（向后兼容）
            if (chargerId == null && params.get("stationId") != null) {
                String stationIdStr = params.get("stationId").toString().trim();
                if (!stationIdStr.isEmpty()) {
                    try {
                        chargerId = Long.parseLong(stationIdStr);
                    } catch (NumberFormatException e) {
                        log.warn("无效的充电桩ID参数: {}", stationIdStr);
                    }
                }
            }
            
            if (socket == null && params.get("socketId") != null) {  
                String socketIdStr = params.get("socketId").toString().trim();
                if (!socketIdStr.isEmpty()) {
                    try {
                        socket = Integer.parseInt(socketIdStr);
                    } catch (NumberFormatException e) {
                        log.warn("无效的端口ID参数: {}", socketIdStr);
                    }
                }
            }
            
            // 验证基本参数
            if (chargerId == null || socket == null) {
                log.error("参数不完整 - 订单ID: {}, 充电桩ID: {}, 端口: {}", orderId, chargerId, socket);
                return ResponseDTO.error("参数不完整，无法获取充电桩ID或端口信息");
            }
            
            log.info("停止充电 - 订单ID: {}, 充电桩ID: {}, 端口: {}", orderId, chargerId, socket);
            
            // 优先使用ChargingService
            if (chargingService != null) {
                try {
                // 构建停止充电请求
                StopChargingRequest request = new StopChargingRequest();
                request.setOrderId(orderId);
                request.setSocket(socket);
                
                    // 调用充电服务停止充电
                    StopChargingResponse response = chargingService.stopCharging(chargerId, request);
                    
                    // 构造响应结果
                    Map<String, Object> result = new HashMap<>();
                    result.put("result", response.getResult());
                    result.put("orderId", response.getOrderId());
                    result.put("reason", response.getReason());
                    result.put("timestamp", response.getTimestamp());
                    
                    // 查询更新后的订单状态
                    ChargingOrder updatedOrder = chargingOrderService.getOrderByOrderId(orderId);
                    if (updatedOrder != null) {
                        result.put("status", updatedOrder.getStatus());
                        
                        // 添加格式化的结束时间
                        if (updatedOrder.getEndTime() != null) {
                            String formattedEndTime = updatedOrder.getEndTime()
                                .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                            result.put("formattedEndTime", formattedEndTime);
                        }
                        
                        // 添加实际充电时间
                        result.put("actualTime", updatedOrder.getActualTime());
                    }
                    
                    // 响应
                    ResponseDTO<Map<String, Object>> responseDTO = ResponseDTO.success(result);
                    responseDTO.setMessage("停止充电指令已发送");
                    return responseDTO;
                } catch (Exception e) {
                    log.error("调用ChargingService停止充电失败: {}", e.getMessage(), e);
                    // 降级使用ChargerService
                }
            }
            
            // 降级方案：使用ChargerService
            Map<String, Object> stopParams = new HashMap<>();
            stopParams.put("chargerId", chargerId);
            stopParams.put("socket", socket);
            stopParams.put("orderId", orderId);
            
            // 调用充电服务
            Map<String, Object> result = chargerService.stopCharging(stopParams);
            
            // 查询最新的订单状态并添加到结果中
            ChargingOrder updatedOrder = chargingOrderService.getOrderByOrderId(orderId);
            if (updatedOrder != null) {
                result.put("status", updatedOrder.getStatus());
                
                // 添加格式化的结束时间
                if (updatedOrder.getEndTime() != null) {
                    String formattedEndTime = updatedOrder.getEndTime()
                        .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    result.put("formattedEndTime", formattedEndTime);
                }
                
                // 添加实际充电时间
                result.put("actualTime", updatedOrder.getActualTime());
            }
            
            // 构造响应
            ResponseDTO<Map<String, Object>> responseDTO;
            if (result != null && (Boolean.TRUE.equals(result.get("success")) || "success".equals(result.get("status")))) {
                responseDTO = ResponseDTO.success(result);
                responseDTO.setMessage("停止充电指令已发送");
            } else {
                responseDTO = ResponseDTO.error("停止充电失败: " + (result != null ? result.get("message") : "未知错误"));
            }
            
            return responseDTO;
        } catch (Exception e) {
            log.error("微信小程序-停止充电异常", e);
            return ResponseDTO.error("停止充电异常: " + e.getMessage());
        }
    }
    
    /**
     * 微信小程序-获取充电信息
     */
    @GetMapping("/charger/charging-info")
    public ResponseDTO<Map<String, Object>> getChargingInfo(@RequestParam String orderId) {
        log.info("微信小程序-获取充电信息请求参数: orderId={}", orderId);
        
        try {
            // 验证参数
            if (orderId == null || orderId.isEmpty()) {
                return ResponseDTO.error("参数不完整，请提供订单ID");
            }
            
            // 先从数据库查询订单
            ChargingOrder order = chargingOrderService.getOrderByOrderId(orderId);
            if (order != null) {
                Map<String, Object> orderInfo = new HashMap<>();
                orderInfo.put("orderId", order.getOrderId());
                orderInfo.put("chargerId", order.getChargerId());
                orderInfo.put("chargerName", order.getChargerName());
                orderInfo.put("stationId", order.getStationId());
                orderInfo.put("stationName", order.getStationName());
                orderInfo.put("socket", order.getSocket());
                orderInfo.put("socketId", order.getSocket());
                orderInfo.put("startTime", order.getStartTime());
                orderInfo.put("endTime", order.getEndTime());
                orderInfo.put("estimatedTime", order.getEstimatedTime());
                orderInfo.put("time", order.getEstimatedTime());
                orderInfo.put("actualTime", order.getActualTime());
                orderInfo.put("minPower", order.getMinPower());
                orderInfo.put("maxPower", order.getMaxPower());
                orderInfo.put("chargeType", order.getChargeType());
                orderInfo.put("paymentType", order.getPaymentType());
                orderInfo.put("status", order.getStatus());
                
                // 🔧 新增：返回充电方案描述
                String finalDescription;
                
                // 🔧 优先处理已完成订单的停止原因描述
                if ((order.getStatus() == 2 || order.getStatus() == 3) && order.getStopReasonDesc() != null) {
                    // 已完成或已取消的订单，优先显示停止原因
                    finalDescription = order.getStopReasonDesc();
                    log.info("使用停止原因作为充电方案描述: {}", finalDescription);
                    
                    // 🔧 同时返回停止原因相关信息
                    orderInfo.put("stopReasonDesc", order.getStopReasonDesc());
                    orderInfo.put("stopReasonType", order.getStopReasonType());
                    orderInfo.put("stopTriggerType", order.getStopTriggerType());
                    orderInfo.put("planDescription", order.getPlanDescription());
                    
                } else if (order.getPlanDescription() != null && !order.getPlanDescription().isEmpty()) {
                    // 使用保存的充电方案描述
                    finalDescription = order.getPlanDescription();
                    log.info("使用保存的充电方案描述: {}", finalDescription);
                    
                } else {
                    // 🔧 修复：如果没有保存的描述，根据支付金额生成描述，而不是基于时间判断
                    if (order.getPaidAmount() != null && order.getPaidAmount() > 0) {
                        // 如果有支付金额，显示支付金额
                        finalDescription = String.format("充电%.1f元", order.getPaidAmount());
                    } else if (order.getEstimatedTime() != null && order.getEstimatedTime() >= 480) {
                        // 只有在没有支付金额且时间>=8小时时才显示充满自停
                        finalDescription = "充满自停";
                    } else {
                        // 其他情况显示时长
                        finalDescription = String.format("充电%d分钟", order.getEstimatedTime() != null ? order.getEstimatedTime() : 30);
                    }
                    log.info("使用默认充电方案描述: {} (支付金额: {}, 预估时间: {}分钟)", 
                            finalDescription, order.getPaidAmount(), order.getEstimatedTime());
                }
                
                orderInfo.put("description", finalDescription);
                
                // 为前端提供格式化的时间字符串
                if (order.getStartTime() != null) {
                    orderInfo.put("formattedStartTime", 
                        order.getStartTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
                if (order.getEndTime() != null) {
                    orderInfo.put("formattedEndTime", 
                        order.getEndTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
                
                // 计算充电进度
                if (order.getStatus() == 1 && order.getStartTime() != null && order.getEstimatedTime() != null) {
                    // 计算已充电时间
                    long elapsedSeconds = java.time.Duration.between(
                            order.getStartTime(), 
                            LocalDateTime.now()
                    ).getSeconds();
                    
                    // 计算总充电时间（秒）
                    long totalSeconds = order.getEstimatedTime() * 60L;
                    
                    // 计算进度百分比（0-100）
                    int progress = (int) Math.min(100, Math.round((double) elapsedSeconds / totalSeconds * 100));
                    
                    orderInfo.put("progress", progress);
                } else if (order.getStatus() == 2) { // 已完成
                    orderInfo.put("progress", 100);
                } else if (order.getStatus() == 0) { // 待充电
                    orderInfo.put("progress", -1);
                        } else {
                    orderInfo.put("progress", 0);
                        }
                
                // 🔧 新增：当充电完成时，返回费用结算信息
                if (order.getStatus() == 2 && order.getSettlementStatus() != null && order.getSettlementStatus() >= 1) {
                    Map<String, Object> feeInfo = new HashMap<>();
                    feeInfo.put("paidAmount", order.getPaidAmount()); // 用户支付金额
                    feeInfo.put("actualAmount", order.getActualAmount()); // 实际费用
                    feeInfo.put("refundAmount", order.getRefundAmount()); // 退费金额
                    feeInfo.put("settlementStatus", order.getSettlementStatus()); // 结算状态：1=已结算，2=已退费
                    feeInfo.put("powerConsumed", order.getPowerConsumed()); // 实际用电量
                    
                    // 添加格式化的结算时间
                    if (order.getSettlementTime() != null) {
                        feeInfo.put("settlementTime", order.getSettlementTime());
                        feeInfo.put("formattedSettlementTime", 
                            order.getSettlementTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    
                    // 添加格式化的退费时间
                    if (order.getRefundTime() != null) {
                        feeInfo.put("refundTime", order.getRefundTime());
                        feeInfo.put("formattedRefundTime", 
                            order.getRefundTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    
                    // 解析费用计算详情
                    if (order.getFeeCalculation() != null && !order.getFeeCalculation().isEmpty()) {
                        try {
                            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                            com.fasterxml.jackson.databind.JsonNode feeDetail = mapper.readTree(order.getFeeCalculation());
                            
                            feeInfo.put("endReason", feeDetail.get("endReason") != null ? feeDetail.get("endReason").asText() : "");
                            feeInfo.put("calculationTime", feeDetail.get("calculationTime") != null ? feeDetail.get("calculationTime").asText() : "");
                        } catch (Exception e) {
                            log.warn("解析费用计算详情失败: {}", e.getMessage());
                        }
                    }
                    
                    orderInfo.put("feeInfo", feeInfo);
                    log.info("返回充电完成的费用信息: {}", feeInfo);
                }
                
                return ResponseDTO.success(orderInfo);
                }
                
            // 如果数据库中没有，则调用设备服务
            Map<String, Object> orderInfo = chargerService.getChargingOrder(orderId);
            
            if (orderInfo != null) {
                return ResponseDTO.success(orderInfo);
            } else {
                return ResponseDTO.error("获取充电信息失败: 未找到相关订单");
            }
        } catch (Exception e) {
            log.error("微信小程序-获取充电信息异常", e);
            return ResponseDTO.error("获取充电信息异常: " + e.getMessage());
        }
    }
    
    /**
     * 微信小程序-获取充电状态
     */
    @GetMapping("/charger/charging-status")
    public ResponseDTO<Map<String, Object>> getChargingStatus(@RequestParam String orderId) {
        log.info("微信小程序-获取充电状态请求参数: orderId={}", orderId);
        
        try {
            // 验证参数
            if (orderId == null || orderId.isEmpty()) {
                return ResponseDTO.error("参数不完整，请提供订单ID");
            }
            
            // 先从数据库查询订单
            ChargingOrder order = chargingOrderService.getOrderByOrderId(orderId);
            if (order != null) {
                Map<String, Object> status = new HashMap<>();
                status.put("orderId", order.getOrderId());
                status.put("status", order.getStatus());
                status.put("chargerId", order.getChargerId());
                status.put("socketId", order.getSocket());
                status.put("socket", order.getSocket());
                status.put("startTime", order.getStartTime());
                status.put("estimatedTime", order.getEstimatedTime());
                status.put("time", order.getEstimatedTime());
                
                // 计算已充电时间（分钟）
                long chargedMinutes = 0;
                if (order.getStartTime() != null) {
                    chargedMinutes = java.time.Duration.between(
                            order.getStartTime(), 
                            LocalDateTime.now()
                    ).toMinutes();
                }
                status.put("chargedMinutes", chargedMinutes);
                
                // 计算充电进度
                if (order.getStatus() == 1 && order.getStartTime() != null && order.getEstimatedTime() != null) {
                    // 计算已充电时间（秒）
                    long elapsedSeconds = java.time.Duration.between(
                            order.getStartTime(), 
                            LocalDateTime.now()
                    ).getSeconds();
                    
                    // 计算总充电时间（秒）
                    long totalSeconds = order.getEstimatedTime() * 60L;
                    
                    // 计算进度百分比（0-100）
                    int progress = (int) Math.min(100, Math.round((double) elapsedSeconds / totalSeconds * 100));
                    
                    status.put("progress", progress);
                } else if (order.getStatus() == 2) { // 已完成
                    status.put("progress", 100);
                } else if (order.getStatus() == 0) { // 待充电
                    status.put("progress", -1);
                } else {
                    status.put("progress", 0);
                }
                
                // 为前端提供格式化的时间字符串
                if (order.getStartTime() != null) {
                    status.put("formattedStartTime", 
                        order.getStartTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
                
                // 🔧 新增：返回停止原因信息，特别是余额不足的情况
                if (order.getStatus() == 2 || order.getStatus() == 3) { // 已完成或已取消
                    status.put("stopReasonDesc", order.getStopReasonDesc());
                    status.put("stopReasonType", order.getStopReasonType());
                    status.put("stopTriggerType", order.getStopTriggerType());
                    status.put("planDescription", order.getPlanDescription());
                    
                    log.info("返回停止原因信息: stopReasonDesc={}, stopTriggerType={}, planDescription={}", 
                            order.getStopReasonDesc(), order.getStopTriggerType(), order.getPlanDescription());
                }
                
                // 🔧 新增：当充电完成时，返回费用结算信息
                if (order.getStatus() == 2 && order.getSettlementStatus() != null && order.getSettlementStatus() >= 1) {
                    Map<String, Object> feeInfo = new HashMap<>();
                    feeInfo.put("paidAmount", order.getPaidAmount()); // 用户支付金额
                    feeInfo.put("actualAmount", order.getActualAmount()); // 实际费用
                    feeInfo.put("refundAmount", order.getRefundAmount()); // 退费金额
                    feeInfo.put("settlementStatus", order.getSettlementStatus()); // 结算状态：1=已结算，2=已退费
                    feeInfo.put("powerConsumed", order.getPowerConsumed()); // 实际用电量
                    
                    // 添加格式化的结算时间
                    if (order.getSettlementTime() != null) {
                        feeInfo.put("settlementTime", order.getSettlementTime());
                        feeInfo.put("formattedSettlementTime", 
                            order.getSettlementTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    
                    // 添加格式化的退费时间
                    if (order.getRefundTime() != null) {
                        feeInfo.put("refundTime", order.getRefundTime());
                        feeInfo.put("formattedRefundTime", 
                            order.getRefundTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    
                    // 解析费用计算详情
                    if (order.getFeeCalculation() != null && !order.getFeeCalculation().isEmpty()) {
                        try {
                            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                            com.fasterxml.jackson.databind.JsonNode feeDetail = mapper.readTree(order.getFeeCalculation());
                            
                            feeInfo.put("endReason", feeDetail.get("endReason") != null ? feeDetail.get("endReason").asText() : "");
                            feeInfo.put("calculationTime", feeDetail.get("calculationTime") != null ? feeDetail.get("calculationTime").asText() : "");
                        } catch (Exception e) {
                            log.warn("解析费用计算详情失败: {}", e.getMessage());
                        }
                    }
                    
                    status.put("feeInfo", feeInfo);
                    log.info("返回充电完成的费用信息: {}", feeInfo);
                }
                
                log.info("✅ 从数据库返回充电状态: orderId={}, status={}", order.getOrderId(), order.getStatus());
                return ResponseDTO.success(status);
            }
                
            // 如果数据库中没有或状态不是充电中，则调用设备服务
            Map<String, Object> status = chargerService.getChargingStatus(orderId);
            
            if (status != null) {
                log.info("✅ 从设备服务返回充电状态: {}", status);
                return ResponseDTO.success(status);
            } else {
                log.warn("❌ 未找到充电状态信息: orderId={}", orderId);
                return ResponseDTO.error("获取充电状态失败: 未找到相关订单或状态");
            }
        } catch (Exception e) {
            log.error("微信小程序-获取充电状态异常", e);
            return ResponseDTO.error("获取充电状态异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定充电桩和端口号的当前活跃充电订单或待充电订单
     */
    @GetMapping("/charger/active-order")
    public ResponseDTO<Map<String, Object>> getActiveOrder(
            @RequestParam("chargerId") Long chargerId,
            @RequestParam("socket") Integer socket) {
        log.info("获取活跃充电订单: chargerId={}, socket={}", chargerId, socket);
        
        try {
            // 查询充电中(1)或待充电(0)的订单
            ChargingOrder order = chargingOrderService.getOne(
                new QueryWrapper<ChargingOrder>()
                    .eq("charger_id", chargerId)
                    .eq("socket", socket)
                    .in("status", 0, 1)  // 状态0表示待充电，1表示充电中
                    .orderByDesc("start_time")
                    .last("LIMIT 1")
            );
            
            if (order == null) {
                return ResponseDTO.error("未找到活跃订单");
            }
            
            // 构建返回结果 - 确保字段名称与前端期望一致
            Map<String, Object> result = new HashMap<>();
            result.put("orderId", order.getOrderId());
            result.put("status", order.getStatus());
            result.put("chargerId", order.getChargerId());
            result.put("stationId", order.getStationId());
            result.put("socketId", order.getSocket());
            result.put("socket", order.getSocket());
            result.put("startTime", order.getStartTime());
            result.put("estimatedTime", order.getEstimatedTime());
            result.put("actualTime", order.getActualTime());
            result.put("chargeType", order.getChargeType());
            result.put("stationName", order.getStationName());
            result.put("chargerName", order.getChargerName());
            
            // 如果前端需要formatted的时间，提供格式化的开始时间
            if (order.getStartTime() != null) {
                String formattedStartTime = order.getStartTime()
                    .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                result.put("formattedStartTime", formattedStartTime);
            }
            
            return ResponseDTO.success(result);
        } catch (Exception e) {
            log.error("获取活跃充电订单失败", e);
            return ResponseDTO.error("获取活跃充电订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取小程序码
     * 根据充电桩ID和端口号生成小程序码
     * 
     * @param chargerId 充电桩ID
     * @param socketNumber 端口号，如果为null则生成主二维码
     * @return 小程序码图片字节数组
     */
    @GetMapping("/charger/qrcode")
    public ResponseEntity<byte[]> getMiniProgramCode(
            @RequestParam("chargerId") Long chargerId,
            @RequestParam(value = "socketNumber", required = false) Integer socketNumber) {
        log.info("获取小程序码请求: chargerId={}, socketNumber={}", chargerId, socketNumber);
        
        try {
            // 构造scene参数，格式为chargerId或chargerId-socketNumber
            String scene = socketNumber == null ? 
                    String.valueOf(chargerId) : 
                    chargerId + "-" + socketNumber;
            
            log.info("生成小程序码，scene={}", scene);
            
            // 调用服务获取小程序码
            byte[] qrCodeBytes = wechatMiniProgramService.getUnlimitedQRCode(scene, "pages/charging/charging");
            
            if (qrCodeBytes == null || qrCodeBytes.length == 0) {
                log.error("生成小程序码失败");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
            
            // 在二维码上添加标识文字
            String labelText = socketNumber == null ? 
                    "ID:" + chargerId : 
                    "ID:" + chargerId + "-" + socketNumber;
            
            // 调用工具类在二维码上添加文字
            byte[] labeledQrCodeBytes = ImageUtils.addTextToImage(qrCodeBytes, labelText);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            
            // 构造文件名
            String filename = socketNumber == null ? 
                    "charger_" + chargerId + "_main.png" : 
                    "charger_" + chargerId + "_socket_" + socketNumber + ".png";
            
            // 添加Content-Disposition头，允许前端自动下载
            headers.add("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            
            log.info("小程序码生成成功，大小: {} 字节", labeledQrCodeBytes.length);
            
            return new ResponseEntity<>(labeledQrCodeBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取小程序码异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取充电桩当前的充电状态
     */
    @GetMapping("/charger/check-status")
    public ResponseDTO<Map<String, Object>> checkChargerStatus(
            @RequestParam("id") Long chargerId,
            @RequestParam("socketId") Integer socket) {
            
        log.info("小程序查询充电桩状态: id={}, socketId={}", chargerId, socket);
        
        // 查询充电桩信息
        Charger charger = chargerService.getChargerById(chargerId);
        if (charger == null) {
            return ResponseDTO.error("充电桩不存在");
        }
        
        log.info("充电桩信息: id={}, name={}, status={}, sockets={}, online={}", 
            charger.getId(), charger.getName(), charger.getStatus(), charger.getSockets(), charger.getOnline());
        
        if (charger.getSocketStatusList() != null) {
            log.info("充电桩原始端口状态数据: {}", charger.getSocketStatusList());
        }
        
        // 查询当前插座是否有活跃的充电订单或待充电的订单
        ChargingOrder activeOrder = chargingOrderService.getOne(
                new QueryWrapper<ChargingOrder>()
                        .eq("charger_id", chargerId)
                        .eq("socket", socket)
                        .in("status", 0, 1)  // 状态0表示已创建未充电，1表示充电中
                        .orderByDesc("start_time")
                        .last("LIMIT 1")
        );
        
        log.info("端口{}的活跃订单查询结果: {}", socket, activeOrder != null ? 
            String.format("找到订单(ID:%s, 状态:%d, 开始时间:%s)", 
                activeOrder.getOrderId(), activeOrder.getStatus(), activeOrder.getStartTime()) : "无活跃订单");
        
        Map<String, Object> result = new HashMap<>();
        result.put("chargerId", chargerId);
        result.put("socketId", socket);
        result.put("socket", socket);
        result.put("chargerName", charger.getName());
        result.put("stationId", charger.getStationId());
        result.put("stationName", getStationName(charger.getStationId()));
        result.put("address", charger.getAddress());
        result.put("status", charger.getStatus());
        
        // 设置插座状态: 0=空闲，1=充电中，2=待充电
        int socketStatus = 0;
        if (activeOrder != null) {
            socketStatus = activeOrder.getStatus() == 1 ? 1 : 2;
            
            // 有活跃的充电订单或待充电订单
            result.put("charging", socketStatus == 1);  // 只有状态为1时才是真正充电中
            result.put("orderId", activeOrder.getOrderId());
            result.put("startTime", activeOrder.getStartTime());
            result.put("orderStatus", activeOrder.getStatus());
            
            // 计算已充电时间（分钟）
            long chargedMinutes = 0;
            if (activeOrder.getStartTime() != null) {
                chargedMinutes = java.time.Duration.between(
                        activeOrder.getStartTime(), 
                        LocalDateTime.now()
                ).toMinutes();
            }
            result.put("chargedMinutes", chargedMinutes);
            result.put("estimatedTime", activeOrder.getEstimatedTime());
            result.put("time", activeOrder.getEstimatedTime());
            
            // 为前端提供格式化的时间字符串
            if (activeOrder.getStartTime() != null) {
                result.put("formattedStartTime", 
                    activeOrder.getStartTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            
            // 计算充电进度
            if (activeOrder.getEstimatedTime() != null && socketStatus == 1) {
                // 计算已充电时间（秒）
                long elapsedSeconds = java.time.Duration.between(
                        activeOrder.getStartTime(), 
                        LocalDateTime.now()
                ).getSeconds();
                
                // 计算总充电时间（秒）
                long totalSeconds = activeOrder.getEstimatedTime() * 60L;
                
                // 计算进度百分比（0-100）
                int progress = (int) Math.min(100, Math.round((double) elapsedSeconds / totalSeconds * 100));
                
                result.put("progress", progress);
            } else {
                result.put("progress", socketStatus == 1 ? 0 : -1);  // -1表示待充电状态
            }
        } else {
            // 没有活跃的充电订单
            result.put("charging", false);
            result.put("orderId", null);
            result.put("startTime", null);
            result.put("chargedMinutes", 0);
            result.put("estimatedTime", 0);
            result.put("progress", 0);
        }
        result.put("socketStatus", socketStatus);
        
        return ResponseDTO.success(result);
    }

    /**
     * 设置不同类型充电桩的特定信息
     */
    private void setChargerTypeInfo(ChargerDTO chargerDTO, Charger charger) {
        if (chargerDTO == null || charger == null) {
            return;
        }
        
        // 根据充电桩类型设置特定信息
        // 类型为1-智能充电插座，2-新型盒式充电桩，3-电动车充电柜等
        switch (charger.getType()) {
            case 1:
                chargerDTO.setTypeDesc("智能充电插座");
                break;
            case 2:
                chargerDTO.setTypeDesc("新型盒式充电桩");
                break;
            case 3:
                chargerDTO.setTypeDesc("电动车充电柜");
                break;
            default:
                chargerDTO.setTypeDesc("未知类型");
        }
    }

    /**
     * 根据设备当前位置获取充电站列表
     */
    @GetMapping("/station/list")
    public ResponseDTO<List<Map<String, Object>>> getStationList(
            @RequestParam(required = false) Double longitude,
            @RequestParam(required = false) Double latitude,
            @RequestParam(defaultValue = "5000") Double radius) {
        
        log.info("微信小程序获取充电站列表: longitude={}, latitude={}, radius={}", longitude, latitude, radius);
        
        try {
            // 查询充电站列表
            List<Map<String, Object>> stationList = new ArrayList<>();
            
            // 如果提供了位置信息，则查询附近的充电站
            if (longitude != null && latitude != null) {
                // 这里使用ChargerService查询附近的充电桩
                List<ChargerDTO> chargerDTOs = chargerService.findNearby(longitude, latitude, radius);
                
                // 将DTO转换为Charger对象以便后续处理
                List<Charger> chargers = new ArrayList<>();
                for (ChargerDTO dto : chargerDTOs) {
                    Charger charger = new Charger();
                    BeanUtils.copyProperties(dto, charger);
                    chargers.add(charger);
                }
                
                // 按照充电站分组，避免重复
                Map<Long, Map<String, Object>> stationMap = new HashMap<>();
                
                for (Charger charger : chargers) {
                    Long stationId = charger.getStationId();
                    
                    // 如果该充电站已经存在，则跳过
                    if (stationMap.containsKey(stationId)) {
                        // 更新充电桩数量
                        Map<String, Object> station = stationMap.get(stationId);
                        Integer count = (Integer) station.getOrDefault("chargerCount", 0);
                        station.put("chargerCount", count + 1);
                        
                        // 更新可用充电桩数量
                        if (charger.getStatus() == 0) { // 假设0表示可用状态
                            Integer availableCount = (Integer) station.getOrDefault("availableCount", 0);
                            station.put("availableCount", availableCount + 1);
                        }
                        
                        continue;
                    }
                    
                    // 创建新的充电站数据
                    Map<String, Object> station = new HashMap<>();
                    station.put("id", stationId);
                    station.put("name", getStationName(charger.getStationId()));
                    station.put("address", charger.getAddress());
                    station.put("latitude", charger.getLatitude());
                    station.put("longitude", charger.getLongitude());
                    station.put("chargerCount", 1);
                    station.put("availableCount", charger.getStatus() == 0 ? 1 : 0); // 假设0表示可用状态
                    
                    // 计算距离
                    if (longitude != null && latitude != null && charger.getLongitude() != null && charger.getLatitude() != null) {
                        double distance = calculateDistance(
                                latitude, longitude, 
                                charger.getLatitude(), charger.getLongitude()
                        );
                        station.put("distance", Math.round(distance * 10) / 10.0); // 保留一位小数
                    } else {
                        station.put("distance", null);
                    }
                    
                    stationMap.put(stationId, station);
                }
                
                // 将充电站集合转换为列表
                stationList.addAll(stationMap.values());
                
                // 按距离排序
                stationList.sort((s1, s2) -> {
                    Double d1 = (Double) s1.get("distance");
                    Double d2 = (Double) s2.get("distance");
                    if (d1 == null && d2 == null) return 0;
                    if (d1 == null) return 1;
                    if (d2 == null) return -1;
                    return d1.compareTo(d2);
                });
            } else {
                // 如果没有提供位置信息，则获取所有充电站
                List<ChargerDTO> allChargerDTOs = chargerService.findAll();
                
                // 将DTO转换为Charger对象以便后续处理
                List<Charger> allChargers = new ArrayList<>();
                for (ChargerDTO dto : allChargerDTOs) {
                    Charger charger = new Charger();
                    BeanUtils.copyProperties(dto, charger);
                    allChargers.add(charger);
                }
                Map<Long, Map<String, Object>> stationMap = new HashMap<>();
                
                for (Charger charger : allChargers) {
                    Long stationId = charger.getStationId();
                    
                    if (stationMap.containsKey(stationId)) {
                        // 更新充电桩数量
                        Map<String, Object> station = stationMap.get(stationId);
                        Integer count = (Integer) station.getOrDefault("chargerCount", 0);
                        station.put("chargerCount", count + 1);
                        
                        // 更新可用充电桩数量
                        if (charger.getStatus() == 0) { // 假设0表示可用状态
                            Integer availableCount = (Integer) station.getOrDefault("availableCount", 0);
                            station.put("availableCount", availableCount + 1);
                        }
                        
                        continue;
                    }
                    
                    // 创建新的充电站数据
                    Map<String, Object> station = new HashMap<>();
                    station.put("id", stationId);
                    station.put("name", getStationName(charger.getStationId()));
                    station.put("address", charger.getAddress());
                    station.put("latitude", charger.getLatitude());
                    station.put("longitude", charger.getLongitude());
                    station.put("chargerCount", 1);
                    station.put("availableCount", charger.getStatus() == 0 ? 1 : 0); // 假设0表示可用状态
                    
                    stationMap.put(stationId, station);
                }
                
                // 将充电站集合转换为列表
                stationList.addAll(stationMap.values());
            }
            
            return ResponseDTO.success(stationList);
        } catch (Exception e) {
            log.error("获取充电站列表失败", e);
            return ResponseDTO.error("获取充电站列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 计算两个经纬度点之间的距离（使用Haversine公式）
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 距离，单位为公里
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 地球平均半径，单位为公里
        final double EARTH_RADIUS = 6371.0;
        
        // 转换为弧度
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        
        // Haversine公式
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        // 距离，单位为公里
        return EARTH_RADIUS * c;
    }

    /**
     * 获取指定充电站内的充电桩列表
     */
    @GetMapping("/station/chargers")
    public ResponseDTO<List<Map<String, Object>>> getChargerListByStation(
            @RequestParam("stationId") Long stationId) {
        
        log.info("微信小程序获取充电站内充电桩列表: stationId={}", stationId);
        
        try {
            // 查询该充电站内的所有充电桩
            List<ChargerDTO> chargerDTOs = chargerService.findByStationId(stationId);
            
            if (chargerDTOs.isEmpty()) {
                return ResponseDTO.error("未找到该充电站的充电桩");
            }
            
            // 将DTO转换为Charger对象以便后续处理
            List<Charger> chargers = new ArrayList<>();
            for (ChargerDTO dto : chargerDTOs) {
                Charger charger = new Charger();
                BeanUtils.copyProperties(dto, charger);
                chargers.add(charger);
            }
            
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (Charger charger : chargers) {
                Map<String, Object> chargerMap = new HashMap<>();
                chargerMap.put("id", charger.getId());
                chargerMap.put("name", charger.getName());
                chargerMap.put("code", charger.getCode());
                chargerMap.put("type", charger.getType());
                chargerMap.put("status", charger.getStatus());
                chargerMap.put("online", charger.getOnline());
                chargerMap.put("sockets", charger.getSockets());
                
                // 查询各插座状态
                List<Map<String, Object>> socketList = new ArrayList<>();
                
                if (charger.getSockets() != null && charger.getSockets() > 0) {
                    for (int i = 1; i <= charger.getSockets(); i++) {
                        int currentSocket = i;
                        
                        // 查询当前插座是否有活跃的充电订单
                        ChargingOrder activeOrder = chargingOrderService.getOne(
                                new QueryWrapper<ChargingOrder>()
                                        .eq("charger_id", charger.getId())
                                        .eq("socket", currentSocket)
                                        .eq("status", 1)
                                        .orderByDesc("start_time")
                                        .last("LIMIT 1")
                        );
                        
                        Map<String, Object> socketMap = new HashMap<>();
                        socketMap.put("id", currentSocket);
                        socketMap.put("status", activeOrder != null ? 1 : 0); // 1表示充电中，0表示空闲
                        
                        if (activeOrder != null) {
                            socketMap.put("orderId", activeOrder.getOrderId());
                            socketMap.put("startTime", activeOrder.getStartTime());
                            
                            // 🔧 重要修复：先减1秒再计算，避免1分钟1秒被算成2分钟
                            long totalSeconds = java.time.Duration.between(
                                    activeOrder.getStartTime(), 
                                    LocalDateTime.now()
                            ).getSeconds();
                            if (totalSeconds > 0) {
                                totalSeconds = totalSeconds - 1; // 减1秒
                            }
                            socketMap.put("chargedMinutes", Math.max(0, totalSeconds / 60));
                            socketMap.put("estimatedTime", activeOrder.getEstimatedTime());
                        }
                        
                        socketList.add(socketMap);
                    }
                }
                
                chargerMap.put("socketList", socketList);
                result.add(chargerMap);
            }
            
            return ResponseDTO.success(result);
        } catch (Exception e) {
            log.error("获取充电站内充电桩列表失败", e);
            return ResponseDTO.error("获取充电站内充电桩列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的历史充电订单
     */
    @GetMapping("/user/order-history")
    public ResponseDTO<List<Map<String, Object>>> getUserOrderHistory(
            @RequestParam("openId") String openId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "10") Integer size) {
        
        log.info("微信小程序获取用户历史充电订单: openId={}, page={}, size={}", openId, page, size);
        
        try {
            // 查询该用户的历史充电订单
            long offset = (page - 1) * size;
            List<ChargingOrder> orders = chargingOrderService.list(
                    new QueryWrapper<ChargingOrder>()
                            .eq("open_id", openId)
                            .orderByDesc("create_time")
                            .last("LIMIT " + offset + "," + size)
            );
            
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (ChargingOrder order : orders) {
                Map<String, Object> orderMap = new HashMap<>();
                
                orderMap.put("orderId", order.getOrderId());
                orderMap.put("status", order.getStatus());
                orderMap.put("chargerId", order.getChargerId());
                orderMap.put("socketId", order.getSocket());
                
                // 查询充电桩信息
                Charger charger = chargerService.getById(order.getChargerId());
                if (charger != null) {
                    orderMap.put("chargerName", charger.getName());
                    orderMap.put("stationId", charger.getStationId());
                    orderMap.put("stationName", getStationName(charger.getStationId()));
                    orderMap.put("address", charger.getAddress());
                }
                
                // 处理时间信息
                if (order.getStartTime() != null) {
                    orderMap.put("startTime", order.getStartTime());
                    orderMap.put("formattedStartTime", 
                        order.getStartTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
                
                if (order.getEndTime() != null) {
                    orderMap.put("endTime", order.getEndTime());
                    orderMap.put("formattedEndTime", 
                        order.getEndTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    
                    // 计算实际充电时间（分钟）
                    long chargedMinutes = java.time.Duration.between(order.getStartTime(), order.getEndTime()).toMinutes();
                    orderMap.put("chargedMinutes", chargedMinutes);
                } else if (order.getStatus() == 1) { // 充电中
                    // 计算当前已充电时间（分钟）
                    long chargedMinutes = java.time.Duration.between(order.getStartTime(), LocalDateTime.now()).toMinutes();
                    orderMap.put("chargedMinutes", chargedMinutes);
                } else {
                    orderMap.put("chargedMinutes", 0);
                }
                
                // 充电金额信息
                orderMap.put("amount", order.getBalance());
                orderMap.put("estimatedTime", order.getEstimatedTime());
                orderMap.put("paymentType", order.getPaymentType());
                
                // 🔧 新增：返回充电方案描述
                String finalDescription;
                
                // 🔧 优先处理已完成订单的停止原因描述
                if ((order.getStatus() == 2 || order.getStatus() == 3) && order.getStopReasonDesc() != null) {
                    // 已完成或已取消的订单，优先显示停止原因
                    finalDescription = order.getStopReasonDesc();
                    log.info("使用停止原因作为充电方案描述: {}", finalDescription);
                    
                    // 🔧 同时返回停止原因相关信息
                    orderMap.put("stopReasonDesc", order.getStopReasonDesc());
                    orderMap.put("stopReasonType", order.getStopReasonType());
                    orderMap.put("stopTriggerType", order.getStopTriggerType());
                    orderMap.put("planDescription", order.getPlanDescription());
                    
                } else if (order.getPlanDescription() != null && !order.getPlanDescription().isEmpty()) {
                    // 使用保存的充电方案描述
                    finalDescription = order.getPlanDescription();
                    log.info("使用保存的充电方案描述: {}", finalDescription);
                    
                } else {
                    // 🔧 修复：如果没有保存的描述，根据支付金额生成描述，而不是基于时间判断
                    if (order.getPaidAmount() != null && order.getPaidAmount() > 0) {
                        // 如果有支付金额，显示支付金额
                        finalDescription = String.format("充电%.1f元", order.getPaidAmount());
                    } else if (order.getEstimatedTime() != null && order.getEstimatedTime() >= 480) {
                        // 只有在没有支付金额且时间>=8小时时才显示充满自停
                        finalDescription = "充满自停";
                    } else {
                        // 其他情况显示时长
                        finalDescription = String.format("充电%d分钟", order.getEstimatedTime() != null ? order.getEstimatedTime() : 30);
                    }
                    log.info("使用默认充电方案描述: {} (支付金额: {}, 预估时间: {}分钟)", 
                            finalDescription, order.getPaidAmount(), order.getEstimatedTime());
                }
                
                orderMap.put("description", finalDescription);
                
                // 🔧 新增：添加停止原因信息
                if (order.getStopReasonDesc() != null) {
                    orderMap.put("stopReasonDesc", order.getStopReasonDesc());
                    orderMap.put("stopReasonType", order.getStopReasonType());
                    orderMap.put("stopTriggerType", order.getStopTriggerType());
                    log.info("返回订单停止原因: {} (类型: {}, 触发方式: {})", 
                            order.getStopReasonDesc(), order.getStopReasonType(), order.getStopTriggerType());
                }
                
                // 添加到结果列表
                result.add(orderMap);
            }
            
            return ResponseDTO.success(result);
        } catch (Exception e) {
            log.error("获取用户历史充电订单失败", e);
            return ResponseDTO.error("获取用户历史充电订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取充电桩的所有端口状态 - 诊断接口
     */
    @GetMapping("/charger/port-status")
    public ResponseDTO<Map<String, Object>> getChargerPortStatus(
            @RequestParam("id") Long chargerId) {
        
        log.info("诊断接口 - 获取充电桩所有端口状态: id={}", chargerId);
        
        try {
            // 查询充电桩信息
            Charger charger = chargerService.getById(chargerId);
            if (charger == null) {
                return ResponseDTO.error("充电桩不存在");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("chargerId", chargerId);
            result.put("name", charger.getName());
            result.put("code", charger.getCode());
            result.put("status", charger.getStatus());
            result.put("online", charger.getOnline());
            result.put("sockets", charger.getSockets());
            result.put("rawSocketStatus", charger.getSocketStatusList());
            
            // 查询所有端口的活跃订单状态
            Map<Integer, Map<String, Object>> socketStatus = new HashMap<>();
            
            if (charger.getSockets() != null && charger.getSockets() > 0) {
                for (int i = 1; i <= charger.getSockets(); i++) {
                    int currentSocket = i;
                    
                    // 查询当前插座是否有活跃的充电订单
                    ChargingOrder activeOrder = chargingOrderService.getOne(
                            new QueryWrapper<ChargingOrder>()
                                    .eq("charger_id", chargerId)
                                    .eq("socket", currentSocket)
                                    .eq("status", 1)
                                    .orderByDesc("start_time")
                                    .last("LIMIT 1")
                    );
                    
                    Map<String, Object> portInfo = new HashMap<>();
                    portInfo.put("portNumber", currentSocket);
                    portInfo.put("hasActiveOrder", activeOrder != null);
                    
                    if (activeOrder != null) {
                        portInfo.put("orderId", activeOrder.getOrderId());
                        portInfo.put("orderStatus", activeOrder.getStatus());
                        portInfo.put("startTime", activeOrder.getStartTime());
                        
                        // 计算已充电时间（分钟）
                        if (activeOrder.getStartTime() != null) {
                            long chargedMinutes = java.time.Duration.between(
                                    activeOrder.getStartTime(), 
                                    LocalDateTime.now()
                            ).toMinutes();
                            portInfo.put("chargedMinutes", chargedMinutes);
                        }
                    }
                    
                    socketStatus.put(currentSocket, portInfo);
                }
            }
            
            result.put("portStatus", socketStatus);
            
            // 查询所有状态为已创建(0)或充电中(1)的充电订单
            List<ChargingOrder> allActiveOrders = chargingOrderService.list(
                    new QueryWrapper<ChargingOrder>()
                            .eq("charger_id", chargerId)
                            .in("status", 0, 1)
                            .orderByDesc("create_time")
            );
            
            List<Map<String, Object>> activeOrders = new ArrayList<>();
            for (ChargingOrder order : allActiveOrders) {
                Map<String, Object> orderInfo = new HashMap<>();
                orderInfo.put("orderId", order.getOrderId());
                orderInfo.put("status", order.getStatus());
                orderInfo.put("socket", order.getSocket());
                orderInfo.put("startTime", order.getStartTime());
                orderInfo.put("estimatedTime", order.getEstimatedTime());
                
                if (order.getStartTime() != null) {
                    orderInfo.put("formattedStartTime", 
                        order.getStartTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
                
                activeOrders.add(orderInfo);
            }
            
            result.put("activeOrders", activeOrders);
            
            return ResponseDTO.success(result);
        } catch (Exception e) {
            log.error("获取充电桩端口状态失败", e);
            return ResponseDTO.error("获取充电桩端口状态失败: " + e.getMessage());
        }
    }

    /**
     * 创建默认的插座状态列表
     * 当设备没有返回原始状态数据时使用
     */
    private void createDefaultSocketStatusList(Charger charger, List<Integer> socketStatusList, Integer socketIdInt, ChargingOrder activeOrder) {
        if (charger.getSockets() != null && charger.getSockets() > 0) {
            for (int i = 0; i < charger.getSockets(); i++) {
                // 默认所有插座为空闲状态(0)
                socketStatusList.add(0);
            }
            
            // 如果当前查询的插座有活跃订单，设置对应插座状态
            if (activeOrder != null && socketIdInt > 0 && socketIdInt <= charger.getSockets()) {
                // 设置状态: 0=空闲，1=充电中，2=待充电（已创建订单但未开始充电）
                int statusValue = activeOrder.getStatus() == 1 ? 1 : 2;
                socketStatusList.set(socketIdInt - 1, statusValue);
            }
        }
        
        log.info("使用基于订单状态创建的默认端口状态列表: {}", socketStatusList);
    }

    /**
     * 根据充电站ID获取充电站名称
     * @param stationId 充电站ID
     * @return 充电站名称，如果未找到则返回"未知充电站"
     */
    private String getStationName(Long stationId) {
        if (stationId == null) {
            return "未知充电站";
        }
        try {
            Optional<Station> stationOpt = stationMapper.findById(stationId);
            return stationOpt.map(Station::getName).orElse("未知充电站");
        } catch (Exception e) {
            log.error("获取充电站名称失败，ID: {}", stationId, e);
            return "未知充电站";
        }
    }

    /**
     * 🔧 新增：微信小程序-获取实时充电费用
     * 用于充电过程中查询当前费用和电量
     */
    @GetMapping("/charger/real-time-fee")
    public ResponseDTO<Map<String, Object>> getRealTimeFee(@RequestParam String orderId) {
        log.info("微信小程序-获取实时充电费用请求参数: orderId={}", orderId);
        
        try {
            // 验证参数
            if (orderId == null || orderId.isEmpty()) {
                return ResponseDTO.error("参数不完整，请提供订单ID");
            }
            
            // 查询订单信息
            ChargingOrder order = chargingOrderService.getOrderByOrderId(orderId);
            if (order == null) {
                // 🔧 新增：如果通过订单ID找不到，尝试解析特殊格式的订单ID
                // 格式可能是：充电桩ID_端口号_时间戳（如：13_1_1749455043836）
                log.info("通过订单ID未找到订单，尝试解析特殊格式：{}", orderId);
                
                Long parsedChargerId = null;
                Integer parsedSocket = null;
                
                if (orderId.contains("_")) {
                    String[] parts = orderId.split("_");
                    if (parts.length >= 2) {
                        try {
                            parsedChargerId = Long.parseLong(parts[0]);
                            parsedSocket = Integer.parseInt(parts[1]);
                            log.info("解析特殊格式订单ID：充电桩ID={}, 端口={}", parsedChargerId, parsedSocket);
                        } catch (NumberFormatException e) {
                            log.warn("无法解析订单ID格式：{}", orderId);
                        }
                    }
                }
                
                // 如果解析成功，查找该充电桩端口的正在充电订单
                if (parsedChargerId != null && parsedSocket != null) {
                    order = chargingOrderService.getOne(
                        new QueryWrapper<ChargingOrder>()
                            .eq("charger_id", parsedChargerId)
                            .eq("socket", parsedSocket)
                            .in("status", 0, 1) // 0=待充电，1=充电中
                            .orderByDesc("start_time")
                            .last("LIMIT 1")
                    );
                    
                    if (order != null) {
                        log.info("通过充电桩ID和端口找到订单：{}", order.getOrderId());
                    }
                }
                
                // 如果还是没找到
                if (order == null) {
                    return ResponseDTO.error("未找到对应的充电订单");
                }
            }
            
            Map<String, Object> feeInfo = new HashMap<>();
            
            if (order.getStatus() == 1) { // 充电中
                // 计算实时费用
                int actualChargingMinutes = 0;
                if (order.getStartTime() != null) {
                    actualChargingMinutes = (int) java.time.Duration.between(
                            order.getStartTime(), 
                            LocalDateTime.now()
                    ).toMinutes();
                }
                
                // 🔧 修改：优先使用设备上报的实际电量，避免使用备用估算
                BigDecimal powerConsumed;
                Integer currentPower = order.getCurrentPower(); // 获取设备实时上报的最大功率
                
                if (order.getPowerConsumed() != null && order.getPowerConsumed() >= 0) {
                    // 🔧 第一优先级：使用设备0x89上报的准确累计电量（最可靠的数据源）
                    powerConsumed = BigDecimal.valueOf(order.getPowerConsumed())
                        .setScale(3, RoundingMode.HALF_UP);
                    log.info("✅ 使用设备0x89上报的准确累计电量: {}度 (订单: {})", powerConsumed, orderId);
                } else if (currentPower != null && currentPower > 0 && actualChargingMinutes > 0) {
                    // 🔧 第二优先级：基于设备上报的最大功率和充电时长计算（备用方案）
                    double calculatedPower = (currentPower / 1000.0) * (actualChargingMinutes / 60.0); // kW * h = kWh
                    powerConsumed = BigDecimal.valueOf(Math.max(0.001, calculatedPower))
                        .setScale(3, RoundingMode.HALF_UP);
                    log.warn("⚠️ 设备未上报累计电量，基于最大功率计算: 最大功率={}W, 时长={}分钟, 计算电量={}度 (订单: {}) - 建议检查设备0x89消息", 
                            currentPower, actualChargingMinutes, powerConsumed, orderId);
                } else {
                    // 🔧 第三优先级：最小费用保障（异常情况）
                    powerConsumed = BigDecimal.valueOf(0.001).setScale(3, RoundingMode.HALF_UP);
                    log.error("❌ 设备未上报电量且无功率信息，使用最小电量: {}度 (订单: {}) - 设备通信异常，请检查", 
                            powerConsumed, orderId);
                }
                
                // 调用费用服务计算实时费用，传递真实功率参数
                BigDecimal estimatedAmount = calculateRealTimeFee(order, actualChargingMinutes, powerConsumed, currentPower);
                
                feeInfo.put("status", "charging");
                feeInfo.put("chargingMinutes", actualChargingMinutes);
                feeInfo.put("powerConsumed", powerConsumed); // 直接使用BigDecimal，保留3位小数
                feeInfo.put("estimatedAmount", estimatedAmount); // 直接使用BigDecimal，保留2位小数
                feeInfo.put("currentPower", currentPower); // 🔧 新增：返回当前实时功率
                
                // 🔧 确保支付金额也精确到分
                if (order.getPaidAmount() != null) {
                    BigDecimal paidAmount = BigDecimal.valueOf(order.getPaidAmount())
                        .setScale(2, RoundingMode.HALF_UP);
                    feeInfo.put("paidAmount", paidAmount);
                } else {
                    feeInfo.put("paidAmount", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                }
                
                log.info("实时费用计算 - 充电时长: {}分钟, 电量: {}kWh (来源: {}), 最大功率: {}W, 费用: {}元", 
                        actualChargingMinutes, powerConsumed, 
                        order.getPowerConsumed() != null && order.getPowerConsumed() >= 0 ? "设备0x89上报" :
                        (currentPower != null && currentPower > 0 ? "功率计算" : "异常保障"),
                        currentPower, estimatedAmount);
                
            } else if (order.getStatus() == 2) { // 已完成
                // 返回结算后的费用信息，确保所有金额精确到分
                feeInfo.put("status", "completed");
                
                BigDecimal paidAmount = BigDecimal.ZERO;
                BigDecimal actualAmount = BigDecimal.ZERO;
                BigDecimal refundAmount = BigDecimal.ZERO;
                BigDecimal powerConsumed = BigDecimal.ZERO;
                
                if (order.getPaidAmount() != null) {
                    paidAmount = BigDecimal.valueOf(order.getPaidAmount())
                        .setScale(2, RoundingMode.HALF_UP);
                }
                feeInfo.put("paidAmount", paidAmount);
                
                if (order.getActualAmount() != null) {
                    actualAmount = BigDecimal.valueOf(order.getActualAmount())
                        .setScale(2, RoundingMode.HALF_UP);
                }
                feeInfo.put("actualAmount", actualAmount);
                
                if (order.getRefundAmount() != null) {
                    refundAmount = BigDecimal.valueOf(order.getRefundAmount())
                        .setScale(2, RoundingMode.HALF_UP);
                }
                feeInfo.put("refundAmount", refundAmount);
                
                if (order.getPowerConsumed() != null) {
                    powerConsumed = BigDecimal.valueOf(order.getPowerConsumed())
                        .setScale(3, RoundingMode.HALF_UP);
                }
                feeInfo.put("powerConsumed", powerConsumed);
                
                feeInfo.put("actualChargingMinutes", order.getActualTime());
                
                // 🔧 修复：检查并处理费用分解数据
                BigDecimal electricityFee = BigDecimal.ZERO;
                BigDecimal serviceFee = BigDecimal.ZERO;
                BigDecimal finalEstimatedAmount = actualAmount;
                
                if (order.getElectricityFee() != null && order.getServiceFee() != null && 
                    (order.getElectricityFee() > 0 || order.getServiceFee() > 0)) {
                    // 从数据库获取保存的费用分解
                    electricityFee = BigDecimal.valueOf(order.getElectricityFee()).setScale(2, RoundingMode.HALF_UP);
                    serviceFee = BigDecimal.valueOf(order.getServiceFee()).setScale(2, RoundingMode.HALF_UP);
                    finalEstimatedAmount = electricityFee.add(serviceFee);
                    
                    log.info("✅ 从数据库获取已完成订单费用分解 - 电费: {}元, 服务费: {}元, 总费用: {}元", 
                            electricityFee, serviceFee, finalEstimatedAmount);
                } else {
                    // 🔧 关键修复：如果数据库中没有费用分解，根据功率和电量重新计算
                    log.warn("⚠️ 数据库中缺少费用分解，开始重新计算订单: {}", orderId);
                    
                    try {
                        // 调用ChargerApiController的费用计算逻辑
                        org.springframework.web.client.RestTemplate restTemplate = new org.springframework.web.client.RestTemplate();
                        String apiUrl = "http://localhost:8084/api/charger/real-time-fee?orderNo=" + orderId;
                        
                        org.springframework.http.ResponseEntity<Map> response = restTemplate.getForEntity(apiUrl, Map.class);
                        
                        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                            Map<String, Object> responseBody = response.getBody();
                            
                            if ("200".equals(responseBody.get("code").toString())) {
                                Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                                
                                if (data != null) {
                                    // 获取计算后的费用数据
                                    if (data.get("electricityFee") != null) {
                                        electricityFee = BigDecimal.valueOf(Double.parseDouble(data.get("electricityFee").toString()))
                                                .setScale(2, RoundingMode.HALF_UP);
                                    }
                                    if (data.get("serviceFee") != null) {
                                        serviceFee = BigDecimal.valueOf(Double.parseDouble(data.get("serviceFee").toString()))
                                                .setScale(2, RoundingMode.HALF_UP);
                                    }
                                    if (data.get("totalFee") != null) {
                                        finalEstimatedAmount = BigDecimal.valueOf(Double.parseDouble(data.get("totalFee").toString()))
                                                .setScale(2, RoundingMode.HALF_UP);
                                    }
                                    
                                    log.info("✅ 通过API重新计算费用分解 - 电费: {}元, 服务费: {}元, 总费用: {}元", 
                                            electricityFee, serviceFee, finalEstimatedAmount);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("调用费用计算API失败: {}", e.getMessage());
                        
                        // 🔧 备用计算方案：使用基础费率计算
                        if (order.getPowerConsumed() != null && order.getActualTime() != null && 
                            order.getPowerConsumed() > 0 && order.getActualTime() > 0) {
                            
                            double electricityPrice = 1.20; // 默认电费单价
                            double serviceFeeHourly = 0.30; // 默认服务费单价
                            
                            double electricityFeeValue = order.getPowerConsumed() * electricityPrice;
                            double serviceFeeValue = (order.getActualTime() / 60.0) * serviceFeeHourly;
                            
                            electricityFee = BigDecimal.valueOf(electricityFeeValue).setScale(2, RoundingMode.HALF_UP);
                            serviceFee = BigDecimal.valueOf(serviceFeeValue).setScale(2, RoundingMode.HALF_UP);
                            finalEstimatedAmount = electricityFee.add(serviceFee);
                            
                            log.info("✅ 使用备用方案计算费用 - 用电量: {}度, 充电时长: {}分钟, 电费: {}元, 服务费: {}元, 总费用: {}元", 
                                    order.getPowerConsumed(), order.getActualTime(), 
                                    electricityFee, serviceFee, finalEstimatedAmount);
                        } else {
                            // 最后的备用方案：使用实际金额或支付金额
                            if (actualAmount.compareTo(BigDecimal.ZERO) > 0) {
                                finalEstimatedAmount = actualAmount;
                            } else if (paidAmount.compareTo(BigDecimal.ZERO) > 0) {
                                finalEstimatedAmount = paidAmount;
                            } else {
                                finalEstimatedAmount = BigDecimal.valueOf(0.01).setScale(2, RoundingMode.HALF_UP);
                            }
                            
                            log.warn("⚠️ 无法计算费用分解，使用总金额: {}元", finalEstimatedAmount);
                        }
                    }
                }
                
                // 🔧 关键修复：确保estimatedAmount有值，小程序前端依赖这个字段显示费用
                feeInfo.put("estimatedAmount", finalEstimatedAmount);
                
                // 添加费用分解信息到返回结果
                feeInfo.put("electricityFee", electricityFee);
                feeInfo.put("serviceFee", serviceFee);
                feeInfo.put("totalFee", finalEstimatedAmount);
                
                // 🔧 新增：构建计算详情
                if (electricityFee.compareTo(BigDecimal.ZERO) > 0 || serviceFee.compareTo(BigDecimal.ZERO) > 0) {
                    Map<String, Object> calculationDetails = new HashMap<>();
                    
                    // 计算电费详情
                    if (order.getPowerConsumed() != null && order.getPowerConsumed() > 0) {
                        // 🔧 修复：不要通过反推计算单价，而是获取真实的收费规则单价
                        Map<String, Object> feeRuleInfo = getFeeRuleForCharger(order.getChargerId());
                        double electricityPrice = 1.20; // 默认电费单价
                        
                        if (feeRuleInfo != null && feeRuleInfo.containsKey("electricityPrice")) {
                            electricityPrice = Double.parseDouble(feeRuleInfo.get("electricityPrice").toString());
                            log.info("✅ 从收费规则获取电费单价: {}元/度", electricityPrice);
                        } else {
                            log.warn("⚠️ 未获取到收费规则，使用默认电费单价: {}元/度", electricityPrice);
                        }
                        
                        calculationDetails.put("electricityCalculation", 
                            String.format("%.3f度 × %.2f元/度 = %.2f元", 
                                order.getPowerConsumed(), electricityPrice, electricityFee.doubleValue()));
                    } else {
                        calculationDetails.put("electricityCalculation", 
                            String.format("电费: %.2f元", electricityFee.doubleValue()));
                    }
                    
                    // 计算服务费详情
                    if (order.getActualTime() != null && order.getActualTime() > 0) {
                        // 🔧 修复：不要通过反推计算单价，而是获取真实的收费规则单价
                        Map<String, Object> feeRuleInfo = getFeeRuleForCharger(order.getChargerId());
                        double serviceFeeHourly = 0.30; // 默认服务费单价
                        
                        if (feeRuleInfo != null && feeRuleInfo.containsKey("serviceFeeHourly")) {
                            serviceFeeHourly = Double.parseDouble(feeRuleInfo.get("serviceFeeHourly").toString());
                            log.info("✅ 从收费规则获取服务费单价: {}元/小时", serviceFeeHourly);
                        } else {
                            log.warn("⚠️ 未获取到收费规则，使用默认服务费单价: {}元/小时", serviceFeeHourly);
                        }
                        
                        calculationDetails.put("serviceCalculation", 
                            String.format("%.2f小时 × %.2f元/小时 = %.2f元", 
                                order.getActualTime() / 60.0, serviceFeeHourly, serviceFee.doubleValue()));
                    } else {
                        calculationDetails.put("serviceCalculation", 
                            String.format("服务费: %.2f元", serviceFee.doubleValue()));
                    }
                    
                    calculationDetails.put("totalCalculation", 
                        String.format("%.2f元 + %.2f元 = %.2f元", 
                            electricityFee.doubleValue(), serviceFee.doubleValue(), actualAmount.doubleValue()));
                    calculationDetails.put("formula", "总费用 = 用电量 × 电费单价 + 充电时长 × 服务费单价");
                    
                    feeInfo.put("calculationDetails", calculationDetails);
                    
                    log.info("✅ 构建已完成订单计算详情完成");
                }
                
                // 🔧 新增：添加停止原因信息
                if (order.getStopReasonDesc() != null) {
                    feeInfo.put("stopReasonDesc", order.getStopReasonDesc());
                    feeInfo.put("stopReasonType", order.getStopReasonType());
                    feeInfo.put("stopTriggerType", order.getStopTriggerType());
                }
                
                log.info("返回已完成订单的完整费用信息 - 支付金额: {}元, 实际费用: {}元, 电费: {}元, 服务费: {}元, 退费金额: {}元, 电量: {}度, 停止原因: {}", 
                        paidAmount, actualAmount, electricityFee, serviceFee, refundAmount, powerConsumed, order.getStopReasonDesc());
            } else {
                // 其他状态
                feeInfo.put("status", "pending");
                
                if (order.getPaidAmount() != null) {
                    feeInfo.put("paidAmount", BigDecimal.valueOf(order.getPaidAmount())
                        .setScale(2, RoundingMode.HALF_UP));
                } else {
                    feeInfo.put("paidAmount", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                }
                
                feeInfo.put("estimatedAmount", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                feeInfo.put("powerConsumed", BigDecimal.ZERO.setScale(3, RoundingMode.HALF_UP));
            }
            
            return ResponseDTO.success(feeInfo);
            
        } catch (Exception e) {
            log.error("获取实时充电费用异常", e);
            return ResponseDTO.error("获取实时充电费用异常: " + e.getMessage());
        }
    }
    
    /**
     * 🔧 计算实时费用（精确到分）
     */
    private BigDecimal calculateRealTimeFee(ChargingOrder order, int chargingMinutes, BigDecimal powerConsumed, Integer currentPower) {
        try {
            // 🔧 修改：统一使用费用结算服务计算，避免多处计算逻辑不一致
            if (chargingFeeSettlementService != null) {
                double actualAmount = chargingFeeSettlementService.calculateActualChargingFee(order, chargingMinutes, powerConsumed.doubleValue(), currentPower);
                BigDecimal result = BigDecimal.valueOf(actualAmount).setScale(2, RoundingMode.HALF_UP);
                log.info("✅ 使用统一费用计算服务: 充电时长={}分钟, 电量={}度, 当前功率={}W, 费用={}元", 
                        chargingMinutes, powerConsumed, currentPower, result);
                return result;
            } else {
                log.warn("⚠️ 费用结算服务未注入，使用简化计算");
                // 简化的备用计算：只收取最低费用
                BigDecimal minFee = new BigDecimal("0.01"); // 最低1分钱
                log.info("使用备用计算: 最低费用={}元", minFee);
                return minFee;
            }
            
        } catch (Exception e) {
            log.error("计算实时费用失败", e);
            return new BigDecimal("0.01"); // 异常情况返回最低消费
        }
    }

    /**
     * 测试微信小程序API连通性
     */
    @GetMapping("/test/wechat")
    public ResponseDTO<Map<String, Object>> testWechatApi() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 1. 测试获取access_token
            String accessToken = wechatConfig.refreshAccessToken();
            result.put("access_token_success", accessToken != null);
            result.put("access_token_length", accessToken != null ? accessToken.length() : 0);
            
            // 2. 测试微信API基本连通性 - 获取小程序基本信息
            String url = "https://api.weixin.qq.com/cgi-bin/account/getaccountbasicinfo?access_token=" + accessToken;
            
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            
            result.put("api_connectivity", response.getStatusCode().is2xxSuccessful());
            result.put("api_response", response.getBody());
            
            log.info("微信API测试结果: {}", result);
            
            return ResponseDTO.success(result);
        } catch (Exception e) {
            log.error("微信API测试失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            return ResponseDTO.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 🔧 新增：获取充电桩的收费规则信息
     */
    private Map<String, Object> getFeeRuleForCharger(Long chargerId) {
        try {
            // 调用费用服务获取收费规则
            String feeServiceUrl = "http://localhost:8083/fee/fee-rules/charger/" + chargerId;
            
            org.springframework.web.client.RestTemplate restTemplate = new org.springframework.web.client.RestTemplate();
            org.springframework.http.ResponseEntity<Map> response = restTemplate.getForEntity(feeServiceUrl, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                
                // 检查返回格式
                if (responseBody.containsKey("code") && "200".equals(responseBody.get("code").toString())) {
                    Object data = responseBody.get("data");
                    if (data instanceof Map) {
                        Map<String, Object> feeRuleData = (Map<String, Object>) data;
                        log.info("✅ 获取收费规则成功 - 充电桩ID: {}, 规则: {}", chargerId, feeRuleData);
                        
                        // 🔧 修复：正确处理功率段配置，根据设备功率匹配对应的价格
                        Map<String, Object> result = new HashMap<>();
                        
                        // 默认使用基础价格
                        Object basePriceObj = feeRuleData.get("basePrice");
                        Object serviceFeeObj = feeRuleData.get("serviceFee");
                        
                        double electricityPrice = basePriceObj != null ? Double.parseDouble(basePriceObj.toString()) : 1.20;
                        double serviceFeeHourly = serviceFeeObj != null ? Double.parseDouble(serviceFeeObj.toString()) : 0.30;
                        
                        // 🔧 修复：获取设备功率并匹配对应的功率段价格
                        Object powerRangesObj = feeRuleData.get("powerRanges");
                        if (powerRangesObj instanceof List) {
                            List<Map<String, Object>> powerRanges = (List<Map<String, Object>>) powerRangesObj;
                            
                            // 🔧 新增：获取设备的实际功率
                            Integer devicePower = getDevicePowerByChargerId(chargerId);
                            log.info("🔧 设备功率: {}W", devicePower);
                            
                            if (devicePower != null && !powerRanges.isEmpty()) {
                                // 根据设备功率匹配对应的功率段
                                Map<String, Object> matchedPowerRange = null;
                                
                                for (Map<String, Object> powerRange : powerRanges) {
                                    Object minPowerObj = powerRange.get("minPower");
                                    Object maxPowerObj = powerRange.get("maxPower");
                                    
                                    if (minPowerObj != null && maxPowerObj != null) {
                                        int minPower = Integer.parseInt(minPowerObj.toString());
                                        int maxPower = Integer.parseInt(maxPowerObj.toString());
                                        
                                        // 检查设备功率是否在此功率段范围内
                                        if (devicePower >= minPower && devicePower <= maxPower) {
                                            matchedPowerRange = powerRange;
                                            log.info("✅ 匹配到功率段: {}W-{}W", minPower, maxPower);
                                            break;
                                        }
                                    }
                                }
                                
                                // 如果匹配到功率段，使用该功率段的价格
                                if (matchedPowerRange != null) {
                                    Object electricityPriceObj = matchedPowerRange.get("electricityPrice");
                                    Object serviceFeeHourlyObj = matchedPowerRange.get("serviceFeeHourly");
                                    
                                    if (electricityPriceObj != null) {
                                        electricityPrice = Double.parseDouble(electricityPriceObj.toString());
                                    }
                                    if (serviceFeeHourlyObj != null) {
                                        serviceFeeHourly = Double.parseDouble(serviceFeeHourlyObj.toString());
                                    }
                                    
                                    log.info("✅ 使用匹配功率段价格 - 设备功率: {}W, 电费: {}元/度, 服务费: {}元/小时", 
                                            devicePower, electricityPrice, serviceFeeHourly);
                                } else {
                                    log.warn("⚠️ 未找到匹配的功率段，使用基础价格 - 设备功率: {}W", devicePower);
                                }
                            } else {
                                log.warn("⚠️ 无设备功率信息或无功率段配置，使用基础价格");
                            }
                        }
                        
                        result.put("electricityPrice", electricityPrice);
                        result.put("serviceFeeHourly", serviceFeeHourly);
                        result.put("ruleName", feeRuleData.get("ruleName"));
                        result.put("ruleCode", feeRuleData.get("ruleCode"));
                        
                        return result;
                    }
                }
            }
            
            log.warn("⚠️ 获取收费规则失败 - 充电桩ID: {}", chargerId);
            return null;
            
        } catch (Exception e) {
            log.error("调用费用服务获取收费规则异常 - 充电桩ID: {}", chargerId, e);
            return null;
        }
    }
    
    /**
     * 🔧 新增：根据充电桩ID获取设备功率
     */
    private Integer getDevicePowerByChargerId(Long chargerId) {
        try {
            // 🔧 调用充电桩服务获取设备信息
            Charger charger = chargerService.getChargerById(chargerId);
            if (charger != null) {
                Integer maxPower = null;
                
                // 根据充电桩类别获取对应的功率字段
                if (charger.getCategory() != null && charger.getCategory() == 1) {
                    // 两轮电动车充电桩，使用bikeChargePower字段
                    maxPower = charger.getBikeChargePower();
                    log.info("✅ 获取两轮电动车充电桩功率成功 - 充电桩ID: {}, 充电功率: {}W", chargerId, maxPower);
                } else if (charger.getCategory() != null && charger.getCategory() == 2) {
                    // 四轮电动汽车充电桩，使用power字段并转换为瓦特
                    if (charger.getPower() != null) {
                        maxPower = (int) (charger.getPower() * 1000); // kW转换为W
                        log.info("✅ 获取四轮电动汽车充电桩功率成功 - 充电桩ID: {}, 功率: {}kW = {}W", chargerId, charger.getPower(), maxPower);
                    }
                } else {
                    log.warn("⚠️ 未知的充电桩类别: {} - 充电桩ID: {}", charger.getCategory(), chargerId);
                }
                
                return maxPower;
            } else {
                log.warn("⚠️ 未找到充电桩信息 - 充电桩ID: {}", chargerId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取设备功率异常 - 充电桩ID: {}", chargerId, e);
            return null;
        }
    }

    /**
     * 获取用户推送设置（小程序专用）
     */
    @GetMapping("/charger/wechat-notification/setting/{userId}")
    public ResponseDTO<WechatNotificationSettingDTO> getUserNotificationSetting(@PathVariable Long userId) {
        log.info("小程序获取用户{}的推送设置", userId);
        try {
            if (wechatNotificationService == null) {
                return ResponseDTO.error("推送服务未启用");
            }
            WechatNotificationSettingDTO setting = wechatNotificationService.getNotificationSetting(userId);
            return ResponseDTO.success(setting);
        } catch (Exception e) {
            log.error("获取用户推送设置失败：userId={}", userId, e);
            return ResponseDTO.error("获取推送设置失败：" + e.getMessage());
        }
    }

    /**
     * 保存用户推送设置（小程序专用）
     */
    @PostMapping("/charger/wechat-notification/setting")
    public ResponseDTO<String> saveNotificationSetting(@RequestBody WechatNotificationSettingDTO settingDTO) {
        log.info("小程序保存用户{}的推送设置", settingDTO.getUserId());
        try {
            if (wechatNotificationService == null) {
                return ResponseDTO.error("推送服务未启用");
            }
            wechatNotificationService.saveNotificationSetting(settingDTO);
            return ResponseDTO.success("保存成功");
        } catch (Exception e) {
            log.error("保存用户推送设置失败：userId={}", settingDTO.getUserId(), e);
            return ResponseDTO.error("保存推送设置失败：" + e.getMessage());
        }
    }

    /**
     * 测试发送推送消息（小程序专用）
     */
    @PostMapping("/charger/wechat-notification/test/{userId}")
    public ResponseDTO<String> testNotification(@PathVariable Long userId, @RequestParam String openId) {
        log.info("小程序测试发送推送消息：userId={}, openId={}", userId, openId);
        try {
            if (wechatNotificationService == null) {
                return ResponseDTO.error("推送服务未启用");
            }
            
            wechatNotificationService.testNotification(userId, openId);
            return ResponseDTO.success("测试消息发送成功");
        } catch (Exception e) {
            log.error("测试发送推送消息失败：userId={}, openId={}", userId, openId, e);
            return ResponseDTO.error("测试发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送推送消息（小程序专用）
     */
    @PostMapping("/charger/wechat-notification/send")
    public ResponseDTO<String> sendNotification(@RequestBody WechatMessageDTO messageDTO) {
        log.info("小程序发送推送消息：{}", messageDTO);
        try {
            if (wechatNotificationService == null) {
                return ResponseDTO.error("推送服务未启用");
            }
            
            wechatNotificationService.sendNotification(messageDTO);
            return ResponseDTO.success("推送消息发送成功");
        } catch (Exception e) {
            log.error("发送推送消息失败：{}", messageDTO, e);
            return ResponseDTO.error("发送失败：" + e.getMessage());
        }
    }

    /**
     * 🔧 新增：查找用户在指定充电桩的最近订单（用于降级方案退款）
     */
    private ChargingOrder findRecentChargingOrder(Long userId, Long chargerId, Integer socket) {
        try {
            if (userId == null || chargerId == null) {
                return null;
            }

            // 🔧 方法1：查找该用户在该充电桩指定端口最近10分钟内创建的订单
            LocalDateTime tenMinutesAgo = LocalDateTime.now().minusMinutes(10);

            QueryWrapper<ChargingOrder> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId)
                   .eq("charger_id", chargerId);

            if (socket != null) {
                wrapper.eq("socket", socket);
            }

            wrapper.ge("create_time", tenMinutesAgo)
                   .orderByDesc("create_time")
                   .last("LIMIT 1");

            ChargingOrder order = chargingOrderService.getOne(wrapper);
            if (order != null) {
                log.info("🔍 找到最近订单（方法1）：订单ID={}, 状态={}, 创建时间={}, 端口={}",
                        order.getOrderId(), order.getStatus(), order.getCreateTime(), order.getSocket());
                return order;
            }

            // 🔧 方法2：如果没找到，扩大范围 - 查找该用户最近10分钟内的任何订单
            log.info("🔍 方法1未找到订单，尝试方法2：查找用户{}最近10分钟内的任何订单", userId);
            QueryWrapper<ChargingOrder> wrapper2 = new QueryWrapper<>();
            wrapper2.eq("user_id", userId)
                    .ge("create_time", tenMinutesAgo)
                    .orderByDesc("create_time")
                    .last("LIMIT 1");

            ChargingOrder order2 = chargingOrderService.getOne(wrapper2);
            if (order2 != null) {
                log.info("🔍 找到最近订单（方法2）：订单ID={}, 状态={}, 创建时间={}, 充电桩ID={}",
                        order2.getOrderId(), order2.getStatus(), order2.getCreateTime(), order2.getChargerId());
                return order2;
            }

            log.warn("⚠️ 未找到用户{}的最近订单", userId);
            return null;
        } catch (Exception e) {
            log.error("❌ 查找最近充电订单失败：用户ID={}, 充电桩ID={}", userId, chargerId, e);
            return null;
        }
    }

    /**
     * 🔧 新增：通过用户最近支付记录进行退款
     */
    private void processRecentPaymentRefund(Long userId, String reason) {
        try {
            log.info("🔧 开始通过最近支付记录退款：用户ID={}, 原因={}", userId, reason);

            // 调用退款服务，传入用户ID，让退款服务自己查找最近的支付记录
            if (refundServiceClient != null) {
                // 构造一个临时订单ID用于退款记录
                String tempOrderId = "TEMP_" + userId + "_" + System.currentTimeMillis();

                com.ebcp.common.dto.ResponseDTO<Object> refundResult = refundServiceClient.processChargingFailureRefund(
                    tempOrderId, reason + " (通过用户ID:" + userId + ")");

                if (refundResult != null && refundResult.getCode() == 200) {
                    log.info("✅ 通过最近支付记录退款成功：用户ID={}", userId);
                } else {
                    log.error("❌ 通过最近支付记录退款失败：用户ID={}, 错误={}", userId,
                            refundResult != null ? refundResult.getMessage() : "退款服务异常");
                }
            }
        } catch (Exception e) {
            log.error("❌ 通过最近支付记录退款异常：用户ID={}", userId, e);
        }
    }
}