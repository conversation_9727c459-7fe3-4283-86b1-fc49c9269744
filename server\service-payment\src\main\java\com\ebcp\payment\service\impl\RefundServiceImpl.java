package com.ebcp.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ebcp.common.dto.ResponseDTO;
import com.ebcp.payment.client.UserServiceClient;
import com.ebcp.payment.dto.RefundRequest;
import com.ebcp.payment.dto.RefundResponse;
import com.ebcp.payment.dto.UpdateBalanceRequest;
import com.ebcp.payment.entity.PaymentRecord;
import com.ebcp.payment.entity.RefundRecord;
import com.ebcp.payment.repository.PaymentRecordRepository;
import com.ebcp.payment.service.RefundService;
import com.ebcp.payment.service.WechatPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 退费服务实现类 - 使用统一的payment_records表记录退费
 */
@Slf4j
@Service
public class RefundServiceImpl implements RefundService {
    
    @Autowired
    private PaymentRecordRepository paymentRecordRepository;

    @Autowired
    private UserServiceClient userServiceClient;

    @Autowired
    private WechatPayService wechatPayService;
    
    @Override
    @Transactional
    public RefundResponse processRefund(RefundRequest request) {
        log.info("🔧 开始处理退费请求：用户ID={}, 退费金额={}, 原支付方式={}, 关联订单={}", 
                request.getUserId(), request.getRefundAmount(), request.getOriginalPaymentMethod(), request.getRelatedOrderId());
        
        try {
            // 检查是否已经退费过（防止重复退费）
            if (request.getRelatedOrderId() != null) {
                List<PaymentRecord> existingRefunds = paymentRecordRepository.findByUserIdAndRelatedTypeOrderByCreateTimeDesc(request.getUserId(), "REFUND");
                
                boolean alreadyRefunded = existingRefunds.stream()
                    .anyMatch(record -> record.getRemark() != null && record.getRemark().contains(request.getRelatedOrderId()));
                
                if (alreadyRefunded) {
                    log.warn("⚠️ 订单{}已经存在退费记录，跳过重复退费", request.getRelatedOrderId());
                    return RefundResponse.builder()
                            .success(false)
                            .message("该订单已经退费过了")
                            .errorCode("ALREADY_REFUNDED")
                            .build();
                }
            }
            
            // 🔧 新增：查找原支付记录并分析退款方式
            List<PaymentRecord> originalPaymentRecords = new ArrayList<>();
            if (request.getRelatedOrderId() != null) {
                originalPaymentRecords = findChargingPaymentRecords(request.getRelatedOrderId());
                log.info("🔧 找到原支付记录{}条", originalPaymentRecords.size());
            }
            
            // 🔧 分析支付记录，确定退款方式和校验金额
            RefundAnalysisResult analysis = analyzePaymentRecordsForRefund(
                    originalPaymentRecords, request.getRefundAmount(), request.getRelatedOrderId());
            
            if (!analysis.isCanRefund()) {
                log.error("❌ 退款校验失败：{}", analysis.getReason());
                return RefundResponse.builder()
                        .success(false)
                        .message(analysis.getReason())
                        .errorCode("REFUND_VALIDATION_FAILED")
                        .build();
            }
            
            log.info("✅ 退款分析通过：方式={}, 金额={}, 原因={}", 
                    analysis.getRefundMethodName(), analysis.getValidatedRefundAmount(), analysis.getReason());
            
            // 获取用户当前余额
            BigDecimal currentBalance = getCurrentUserBalance(request.getUserId());
            
            // 创建退费记录
            PaymentRecord refundRecord = new PaymentRecord();
            String refundNo = generateRefundNo();
            refundRecord.setPaymentNo(refundNo);
            refundRecord.setUserId(request.getUserId());
            refundRecord.setRelatedType("REFUND");
            refundRecord.setRelatedId(0L);
            refundRecord.setAmount(analysis.getValidatedRefundAmount());
            refundRecord.setPaymentMethod(analysis.getRefundMethodName());
            refundRecord.setRemark(buildRefundRemark(request) + "；" + analysis.getReason());
            refundRecord.setStatus(PaymentRecord.STATUS_PROCESSING);
            
            LocalDateTime now = LocalDateTime.now();
            refundRecord.setCreateTime(now);
            refundRecord.setUpdateTime(now);
            
            // 🔧 如果是微信原路退回，保存相关信息
            if ("WECHAT_ORIGINAL".equals(analysis.getRefundMethod())) {
                refundRecord.setTransactionId(analysis.getWechatTransactionId());
                refundRecord.setRemark(refundRecord.getRemark() + "；微信订单号：" + analysis.getWechatTransactionId());
            }
            
            refundRecord = paymentRecordRepository.save(refundRecord);
            log.info("📝 退费记录已创建：ID={}, 退费单号={}", refundRecord.getId(), refundNo);
            
            // 🔧 根据退款方式执行相应的退款逻辑
            boolean refundSuccess = false;
            String errorMessage = null;
            BigDecimal newBalance = currentBalance;
            
            try {
                if ("WECHAT_ORIGINAL".equals(analysis.getRefundMethod())) {
                    // 🔧 微信原路退回
                    log.info("🔧 执行微信原路退回...");
                    refundSuccess = processWechatOriginalRefund(analysis, refundNo);
                    if (refundSuccess) {
                        log.info("✅ 微信原路退款提交成功，等待微信处理结果");
                        // 微信原路退回不影响用户余额
                        newBalance = currentBalance;
                    } else {
                        errorMessage = "微信原路退款失败";
                        log.error("❌ 微信原路退款失败，可能需要人工处理");
                    }
                } else {
                    // 🔧 余额退款
                    log.info("🔧 执行余额退款...");
                    newBalance = currentBalance.add(analysis.getValidatedRefundAmount());
                    boolean balanceUpdated = updateUserBalance(request.getUserId(), newBalance);
                    
                    if (balanceUpdated) {
                        refundSuccess = true;
                        log.info("✅ 余额退款成功：用户ID={}, 退费金额={}, 余额变化：{} -> {}", 
                                request.getUserId(), analysis.getValidatedRefundAmount(), currentBalance, newBalance);
                    } else {
                        errorMessage = "更新用户余额失败";
                        log.error("❌ 更新用户余额失败");
                    }
                }
                
            } catch (Exception e) {
                log.error("❌ 处理退费失败", e);
                errorMessage = "退费处理异常：" + e.getMessage();
            }
            
            // 更新退费记录状态
            if (refundSuccess) {
                refundRecord.setStatus(PaymentRecord.STATUS_SUCCESS);
                refundRecord.setPaymentTime(now);
                refundRecord.setBalance(newBalance);
                if ("WECHAT_ORIGINAL".equals(analysis.getRefundMethod())) {
                    refundRecord.setRemark(refundRecord.getRemark() + "；微信原路退款已提交");
                } else {
                    refundRecord.setRemark(refundRecord.getRemark() + "；余额退款已完成");
                }
            } else {
                refundRecord.setStatus(PaymentRecord.STATUS_FAILED);
                refundRecord.setRemark(refundRecord.getRemark() + "；失败原因：" + errorMessage);
            }
            refundRecord.setUpdateTime(now);
            paymentRecordRepository.save(refundRecord);
            
            log.info("📊 退费处理完成：成功={}, 退费方式={}, 用户余额变化：{} -> {}", 
                    refundSuccess, analysis.getRefundMethodName(), currentBalance, newBalance);
            
            return RefundResponse.builder()
                    .success(refundSuccess)
                    .refundNo(refundRecord.getPaymentNo())
                    .refundAmount(analysis.getValidatedRefundAmount())
                    .refundMethod(analysis.getRefundMethodName())
                    .balanceBefore(currentBalance)
                    .balanceAfter(newBalance)
                    .message(refundSuccess ? ("退费成功 - " + analysis.getRefundMethodName()) : errorMessage)
                    .errorCode(refundSuccess ? null : "REFUND_FAILED")
                    .build();
                    
        } catch (Exception e) {
            log.error("❌ 处理退费请求异常", e);
            return RefundResponse.builder()
                    .success(false)
                    .message("退费处理异常：" + e.getMessage())
                    .errorCode("REFUND_ERROR")
                    .build();
        }
    }
    
    @Override
    @Transactional
    public RefundResponse processChargingFailureRefund(String orderId, String reasonDesc) {
        log.info("🔧 开始处理充电异常退费：订单ID={}, 原因={}", orderId, reasonDesc);

        // 🔧 使用同步锁防止同一订单的并发退款
        synchronized (("REFUND_" + orderId).intern()) {
            try {
                // 🔧 第一步：先查找支付记录获取用户ID
                List<PaymentRecord> paymentRecords = findChargingPaymentRecords(orderId);
                Long userId = null;

                // 🔧 特殊处理：如果订单ID以USER_开头，直接从订单ID提取用户ID
                if (orderId.startsWith("USER_")) {
                    try {
                        String[] parts = orderId.split("_");
                        if (parts.length >= 2) {
                            userId = Long.parseLong(parts[1]);
                            log.info("🔍 从特殊订单ID提取用户ID: {}", userId);

                            // 查找该用户最近的支付记录
                            paymentRecords = findUserRecentPaymentRecords(userId);
                            log.info("🔍 为用户{}找到{}条最近支付记录", userId, paymentRecords.size());
                        }
                    } catch (Exception e) {
                        log.error("❌ 从订单ID提取用户ID失败：{}", orderId, e);
                    }
                }

                if (userId == null) {
                    if (!paymentRecords.isEmpty()) {
                        userId = paymentRecords.get(0).getUserId();
                        log.info("🔍 从支付记录获取用户ID: {}", userId);
                    } else {
                        // 如果没有支付记录，尝试其他方式获取用户ID
                        userId = extractUserIdFromOrderSimple(orderId);
                        log.info("🔍 通过其他方式获取用户ID: {}", userId);
                    }
                }

                if (userId == null) {
                    log.error("❌ 无法确定订单{}的用户ID，无法进行退费", orderId);
                    return RefundResponse.builder()
                            .success(false)
                            .message("无法确定退费用户")
                            .errorCode("CANNOT_DETERMINE_USER")
                            .build();
                }

                // 🔧 第二步：检查该用户是否已存在该订单的退费记录
                List<PaymentRecord> existingRefunds = paymentRecordRepository.findByUserIdAndRelatedTypeOrderByCreateTimeDesc(userId, "REFUND");

                boolean alreadyRefunded = existingRefunds.stream()
                    .anyMatch(record -> {
                        if (record.getRemark() == null) return false;
                        // 精确匹配订单ID
                        return record.getRemark().contains("订单ID:" + orderId) ||
                               record.getRemark().contains("充电异常退费-" + orderId) ||
                               (record.getRelatedId() != null && record.getRelatedId().equals(orderId));
                    });

                if (alreadyRefunded) {
                    log.warn("⚠️ 用户{}的订单{}已存在退费记录，跳过重复退费", userId, orderId);
                    return RefundResponse.builder()
                            .success(false)
                            .message("该订单已经退费过了")
                            .errorCode("ALREADY_REFUNDED")
                            .build();
                }

                log.info("✅ 重复退费检查通过：用户ID={}, 订单ID={}", userId, orderId);
            
                // 🔧 第三步：计算退费金额
                BigDecimal totalRefundAmount = BigDecimal.ZERO;

                if (!paymentRecords.isEmpty()) {
                    // 方案1：找到了支付记录，按原逻辑处理
                    log.info("找到{}条支付记录，按支付记录退费", paymentRecords.size());

                    for (PaymentRecord paymentRecord : paymentRecords) {
                        if (paymentRecord.getStatus().equals(PaymentRecord.STATUS_SUCCESS)) {
                            totalRefundAmount = totalRefundAmount.add(paymentRecord.getAmount());
                        }
                    }

                    if (totalRefundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        log.warn("订单{}没有有效的支付金额", orderId);
                        return RefundResponse.builder()
                                .success(false)
                                .message("没有需要退费的金额")
                                .errorCode("NO_REFUND_AMOUNT")
                                .build();
                    }
                } else {
                // 方案2：没有找到支付记录，可能是余额支付，尝试从订单信息计算退费金额
                log.info("未找到支付记录，可能是余额支付，尝试通过订单计算退费金额");
                
                // 🔧 新增：通过订单信息计算退费金额
                try {
                    // 这里应该调用订单服务获取订单信息，暂时使用固定逻辑
                    // TODO: 实现通过OrderServiceClient获取订单详情
                    
                    // 🔧 修复：避免重复赋值userId，这里userId已经在前面获取过了
                    // 只需要计算退费金额
                    totalRefundAmount = calculateRefundAmountFromOrder(orderId);

                    // userId 已经在前面通过 extractUserIdFromOrderSimple 获取了，这里不需要重新赋值
                    
                    if (totalRefundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        log.warn("无法从订单{}计算退费金额", orderId);
                        return RefundResponse.builder()
                                .success(false)
                                .message("无法确定退费金额")
                                .errorCode("CANNOT_DETERMINE_AMOUNT")
                                .build();
                    }
                    
                    log.info("通过订单信息计算出退费金额：用户ID={}, 金额={}", userId, totalRefundAmount);
                    
                    // 🔧 添加安全检查：如果计算出的退费金额过大，需要人工审核
                    if (totalRefundAmount.compareTo(new BigDecimal("0.10")) > 0) {
                        log.warn("计算出的退费金额{}超过安全阈值0.10元，可能存在问题，建议人工审核", totalRefundAmount);
                        return RefundResponse.builder()
                                .success(false)
                                .message("退费金额异常，需要人工审核")
                                .errorCode("REFUND_AMOUNT_ABNORMAL")
                                .build();
                    }
                    
                } catch (Exception e) {
                    log.error("通过订单计算退费信息失败", e);
                    return RefundResponse.builder()
                            .success(false)
                            .message("无法计算退费信息")
                            .errorCode("CALCULATION_FAILED")
                            .build();
                }
            }
            
                // 🔧 构建退费请求，增强备注信息防重复
                RefundRequest refundRequest = RefundRequest.builder()
                        .userId(userId)
                        .relatedOrderId(orderId)
                        .refundAmount(totalRefundAmount)
                        .originalPaymentId(paymentRecords.isEmpty() ? null : paymentRecords.get(0).getId())
                        .originalPaymentNo(paymentRecords.isEmpty() ? "BALANCE_" + orderId : paymentRecords.get(0).getPaymentNo())
                        .originalPaymentMethod(paymentRecords.isEmpty() ? "余额支付" : determineOriginalPaymentMethod(paymentRecords))
                        .reasonType("CHARGING_FAILED")
                        .reasonDesc(reasonDesc != null ? reasonDesc : "充电启动失败，自动退费")
                        .remark("充电异常退费-订单ID:" + orderId + "-" + (reasonDesc != null ? reasonDesc : "充电启动失败"))
                        .build();

                // 处理退费
                RefundResponse response = processRefund(refundRequest);
                log.info("🔧 充电异常退费处理完成：订单ID={}, 成功={}", orderId, response.getSuccess());
                return response;

            } catch (Exception e) {
                log.error("❌ 处理充电异常退费失败：订单ID={}", orderId, e);
                return RefundResponse.builder()
                        .success(false)
                        .message("充电异常退费处理失败: " + e.getMessage())
                        .errorCode("CHARGING_REFUND_ERROR")
                        .build();
            }
        } // 🔧 同步块结束
    }
    
    @Override
    @Transactional
    public RefundResponse processChargingSettlementRefund(String orderId, Double refundAmount, String reasonDesc) {
        log.info("开始处理充电结算退费：订单ID={}, 退费金额={}, 原因={}", orderId, refundAmount, reasonDesc);
        
        try {
            // 1. 参数验证
            if (refundAmount == null || refundAmount <= 0) {
                log.warn("退费金额无效：{}", refundAmount);
                return RefundResponse.builder()
                        .success(false)
                        .message("退费金额无效")
                        .errorCode("INVALID_REFUND_AMOUNT")
                        .build();
            }
            
            BigDecimal refundAmountBD = BigDecimal.valueOf(refundAmount).setScale(2, RoundingMode.HALF_UP);
            
            // 2. 查找相关的充电支付记录以获取用户ID
            List<PaymentRecord> paymentRecords = findChargingPaymentRecords(orderId);
            Long userId = null;
            
            if (!paymentRecords.isEmpty()) {
                // 从支付记录中获取用户ID
                userId = paymentRecords.get(0).getUserId();
                log.info("从支付记录中获取用户ID：{}", userId);
            } else {
                // 如果没有找到支付记录，尝试通过其他方式获取用户ID
                log.warn("未找到订单{}的支付记录，尝试通过订单服务获取用户ID", orderId);
                userId = extractUserIdFromOrder(orderId);
                
                if (userId == null) {
                    log.error("无法获取订单{}的用户ID", orderId);
                    return RefundResponse.builder()
                            .success(false)
                            .message("无法确定订单对应的用户")
                            .errorCode("USER_NOT_FOUND")
                            .build();
                }
            }
            
            // 3. 检查是否已有该订单的结算退费记录（防重复退费）
            List<PaymentRecord> existingRefunds = paymentRecordRepository.findByUserIdAndRelatedTypeOrderByCreateTimeDesc(userId, "REFUND");
            for (PaymentRecord refund : existingRefunds) {
                if (refund.getRemark() != null && refund.getRemark().contains("充电完成多退少补") && refund.getRemark().contains(orderId)) {
                    log.warn("订单{}已存在结算退费记录，拒绝重复退费", orderId);
                    return RefundResponse.builder()
                            .success(false)
                            .message("该订单已处理过结算退费")
                            .errorCode("DUPLICATE_SETTLEMENT_REFUND")
                            .build();
                }
            }
            
            // 4. 构建退费请求
            RefundRequest refundRequest = RefundRequest.builder()
                    .userId(userId)
                    .relatedOrderId(orderId)
                    .refundAmount(refundAmountBD)
                    .originalPaymentId(paymentRecords.isEmpty() ? null : paymentRecords.get(0).getId())
                    .originalPaymentNo(paymentRecords.isEmpty() ? "SETTLEMENT_" + orderId : paymentRecords.get(0).getPaymentNo())
                    .originalPaymentMethod(paymentRecords.isEmpty() ? "余额支付" : determineOriginalPaymentMethod(paymentRecords))
                    .reasonType("CHARGING_SETTLEMENT")
                    .reasonDesc(reasonDesc != null ? reasonDesc : "充电完成多退少补")
                    .remark("充电结算自动退费")
                    .build();
            
            // 5. 处理退费
            RefundResponse response = processRefund(refundRequest);
            
            if (response.getSuccess()) {
                log.info("充电结算退费成功 - 订单: {}, 用户: {}, 退费金额: {}元", orderId, userId, refundAmount);
            } else {
                log.error("充电结算退费失败 - 订单: {}, 错误: {}", orderId, response.getMessage());
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("处理充电结算退费失败：订单ID={}", orderId, e);
            return RefundResponse.builder()
                    .success(false)
                    .message("充电结算退费处理失败: " + e.getMessage())
                    .errorCode("SETTLEMENT_REFUND_ERROR")
                    .build();
        }
    }
    
    @Override
    public List<RefundRecord> getUserRefundRecords(Long userId) {
        // 从payment_records表中查询退费记录
        List<PaymentRecord> refundPayments = paymentRecordRepository.findByUserIdAndRelatedTypeOrderByCreateTimeDesc(userId, "REFUND");
        
        // 转换为RefundRecord格式（为了保持接口兼容性）
        List<RefundRecord> refundRecords = new ArrayList<>();
        for (PaymentRecord payment : refundPayments) {
            RefundRecord refund = new RefundRecord();
            refund.setId(payment.getId());
            refund.setRefundNo(payment.getPaymentNo());
            refund.setUserId(payment.getUserId());
            refund.setRefundAmount(payment.getAmount());
            refund.setRefundMethod(payment.getPaymentMethod());
            refund.setReasonDesc(payment.getRemark());
            refund.setStatus(payment.getStatus());
            refund.setProcessTime(payment.getPaymentTime());
            refund.setCreateTime(payment.getCreateTime());
            refund.setUpdateTime(payment.getUpdateTime());
            refund.setBalanceAfter(payment.getBalance());
            refundRecords.add(refund);
        }
        
        return refundRecords;
    }
    
    @Override
    public RefundRecord getRefundByNo(String refundNo) {
        Optional<PaymentRecord> payment = paymentRecordRepository.findByPaymentNo(refundNo);
        if (payment.isPresent() && "REFUND".equals(payment.get().getRelatedType())) {
            PaymentRecord p = payment.get();
            RefundRecord refund = new RefundRecord();
            refund.setId(p.getId());
            refund.setRefundNo(p.getPaymentNo());
            refund.setUserId(p.getUserId());
            refund.setRefundAmount(p.getAmount());
            refund.setRefundMethod(p.getPaymentMethod());
            refund.setReasonDesc(p.getRemark());
            refund.setStatus(p.getStatus());
            refund.setProcessTime(p.getPaymentTime());
            refund.setCreateTime(p.getCreateTime());
            refund.setUpdateTime(p.getUpdateTime());
            refund.setBalanceAfter(p.getBalance());
            return refund;
        }
        return null;
    }
    
    @Override
    @Transactional
    public RefundResponse retryRefund(Long refundId) {
        log.info("重试退费：退费记录ID={}", refundId);
        
        Optional<PaymentRecord> paymentOpt = paymentRecordRepository.findById(refundId);
        if (!paymentOpt.isPresent()) {
            return RefundResponse.builder()
                    .success(false)
                    .message("退费记录不存在")
                    .errorCode("REFUND_NOT_FOUND")
                    .build();
        }
        
        PaymentRecord refundRecord = paymentOpt.get();
        
        if (!"REFUND".equals(refundRecord.getRelatedType()) || !refundRecord.getStatus().equals(PaymentRecord.STATUS_FAILED)) {
            return RefundResponse.builder()
                    .success(false)
                    .message("只能重试失败的退费记录")
                    .errorCode("INVALID_STATUS")
                    .build();
        }
        
        // 重新处理退费
        try {
            BigDecimal currentBalance = getCurrentUserBalance(refundRecord.getUserId());
            BigDecimal newBalance = currentBalance.add(refundRecord.getAmount());
            
            boolean balanceUpdated = updateUserBalance(refundRecord.getUserId(), newBalance);
            
            if (balanceUpdated) {
                // 更新状态为成功
                refundRecord.setStatus(PaymentRecord.STATUS_SUCCESS);
                refundRecord.setPaymentTime(LocalDateTime.now());
                refundRecord.setBalance(newBalance);
                refundRecord.setUpdateTime(LocalDateTime.now());
                paymentRecordRepository.save(refundRecord);
                
                return RefundResponse.builder()
                        .success(true)
                        .refundNo(refundRecord.getPaymentNo())
                        .refundAmount(refundRecord.getAmount())
                        .refundMethod(refundRecord.getPaymentMethod())
                        .balanceBefore(currentBalance)
                        .balanceAfter(newBalance)
                        .message("退费重试成功")
                        .build();
            } else {
                return RefundResponse.builder()
                        .success(false)
                        .message("余额更新失败")
                        .errorCode("BALANCE_UPDATE_FAILED")
                        .build();
            }
        } catch (Exception e) {
            log.error("重试退费异常", e);
            return RefundResponse.builder()
                    .success(false)
                    .message("重试退费异常")
                    .errorCode("RETRY_ERROR")
                    .build();
        }
    }
    
    /**
     * 查找充电相关的支付记录
     */
    private List<PaymentRecord> findChargingPaymentRecords(String orderId) {
        log.info("🔍 [查找支付记录] 开始查找订单{}的充电支付记录", orderId);
        
        List<PaymentRecord> paymentRecords = new ArrayList<>();
        
        try {
            // 🔧 方法1：首先通过充电服务获取订单的数据库ID，然后精确匹配related_id
            Long chargingOrderDbId = getChargingOrderDbId(orderId);
            if (chargingOrderDbId != null) {
                log.info("🔍 [查找支付记录] 方法1：通过数据库ID{}查找related_id匹配的支付记录", chargingOrderDbId);
                List<PaymentRecord> relatedRecords = paymentRecordRepository.findByRelatedTypeAndRelatedId("CHARGING", chargingOrderDbId);
                
                for (PaymentRecord record : relatedRecords) {
                    if (record.getStatus().equals(PaymentRecord.STATUS_SUCCESS)) {
                        paymentRecords.add(record);
                        log.info("✅ [查找支付记录] 方法1成功：通过related_id={}找到支付记录：ID={}, 支付单号={}, 金额={}, 支付方式={}", 
                                chargingOrderDbId, record.getId(), record.getPaymentNo(), record.getAmount(), record.getPaymentMethod());
                    }
                }
                
                if (!paymentRecords.isEmpty()) {
                    log.info("✅ [查找支付记录] 方法1完成：通过related_id={}找到{}条匹配的支付记录", chargingOrderDbId, paymentRecords.size());
                    return paymentRecords; // 找到精确匹配的记录，直接返回
                }
            }
            
            // 🔧 方法2：如果方法1失败，通过备注字段精确查找
            if (paymentRecords.isEmpty()) {
                log.info("🔍 [查找支付记录] 方法2：通过备注字段精确查找包含'{}'的记录", orderId);

                // 🔧 特殊处理：如果是USER_开头的订单ID，查找该用户最近的支付记录
                if (orderId.startsWith("USER_")) {
                    try {
                        String[] parts = orderId.split("_");
                        if (parts.length >= 2) {
                            Long userId = Long.parseLong(parts[1]);
                            log.info("🔍 [查找支付记录] 检测到USER_格式，查找用户{}最近的支付记录", userId);

                            // 查找该用户最近的充电支付记录（包括待支付状态）
                            LocalDateTime fiveMinutesAgo = LocalDateTime.now().minusMinutes(5);
                            List<PaymentRecord> userRecords = paymentRecordRepository.findByUserIdAndRelatedTypeOrderByCreateTimeDesc(userId, "CHARGING");

                            for (PaymentRecord record : userRecords) {
                                if (record.getCreateTime() != null && record.getCreateTime().isAfter(fiveMinutesAgo)) {
                                    Integer status = record.getStatus();
                                    // 包括成功(1)、待支付(3)状态
                                    if (status != null && (status.equals(PaymentRecord.STATUS_SUCCESS) || status.equals(3))) {
                                        log.info("✅ [查找支付记录] 找到用户{}的支付记录：ID={}, 金额={}, 状态={}",
                                            userId, record.getId(), record.getAmount(), record.getStatus());
                                        paymentRecords.add(record);
                                        break; // 找到一条就够了
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("❌ [查找支付记录] 处理USER_格式订单ID失败", e);
                    }
                }

                // 如果USER_格式没找到，继续原有的备注查找逻辑
                if (paymentRecords.isEmpty()) {
                    List<PaymentRecord> allChargingRecords = paymentRecordRepository.findByRelatedTypeAndStatus("CHARGING", PaymentRecord.STATUS_SUCCESS);
                    log.info("📊 [查找支付记录] 方法2：查询到{}条成功的充电支付记录", allChargingRecords.size());

                    for (PaymentRecord record : allChargingRecords) {
                        if (record.getRemark() != null && record.getRemark().contains(orderId)) {
                            paymentRecords.add(record);
                            log.info("✅ [查找支付记录] 方法2成功：通过备注字段找到匹配的支付记录：ID={}, 备注={}",
                                    record.getId(), record.getRemark());
                            break; // 只取第一个精确匹配的
                        }
                    }
                }
            }
            
            // 🔧 方法3：如果前面都失败，通过时间戳匹配（最后的备选方案）
            if (paymentRecords.isEmpty()) {
                log.info("🔍 [查找支付记录] 方法3：通过时间戳匹配查找最近的未关联支付记录");
                
                // 提取订单号中的时间戳
                String orderNoDigits = orderId.replaceAll("[^0-9]", "");
                if (orderNoDigits.length() > 10) {
                    // 查找最近30分钟内的未关联支付记录
                    LocalDateTime thirtyMinutesAgo = LocalDateTime.now().minusMinutes(30);
                    List<PaymentRecord> unlinkedRecords = paymentRecordRepository.findByRelatedTypeAndStatusAndRelatedId(
                            "CHARGING", PaymentRecord.STATUS_SUCCESS, 0L);
                    
                    log.info("📊 [查找支付记录] 方法3：找到{}条未关联的充电支付记录", unlinkedRecords.size());
                    
                    // 从未关联记录中查找时间戳匹配的
                    for (PaymentRecord record : unlinkedRecords) {
                        if (record.getCreateTime() != null && record.getCreateTime().isAfter(thirtyMinutesAgo)) {
                            if (isTimeStampMatch(record.getPaymentNo(), orderNoDigits)) {
                                paymentRecords.add(record);
                                log.info("✅ [查找支付记录] 方法3成功：找到时间戳匹配记录：ID={}, 支付单号={}", 
                                        record.getId(), record.getPaymentNo());
                                break; // 只取第一个匹配的，避免匹配多个
                            }
                        }
                    }
                }
            }
            
            // 如果所有方法都失败了，记录错误
            if (paymentRecords.isEmpty()) {
                log.error("❌ [查找支付记录] 所有查找方法都失败：未找到订单{}对应的支付记录", orderId);
            }
            
        } catch (Exception e) {
            log.error("❌ [查找支付记录] 查找过程异常：{}", e.getMessage(), e);
        }
        
        // 记录最终结果
        BigDecimal totalAmount = paymentRecords.stream()
                .map(PaymentRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        log.info("✅ [查找支付记录] 最终结果：订单{}找到{}条支付记录，总金额：{}", orderId, paymentRecords.size(), totalAmount);
        
        // 详细记录每条找到的支付记录
        for (int i = 0; i < paymentRecords.size(); i++) {
            PaymentRecord record = paymentRecords.get(i);
            log.info("📝 [查找支付记录] 第{}条记录：ID={}, 支付单号={}, 用户ID={}, 金额={}, 状态={}, related_id={}, 支付方式={}, 微信订单号={}, 时间={}, 备注={}", 
                    i + 1, record.getId(), record.getPaymentNo(), record.getUserId(), record.getAmount(), 
                    record.getStatus(), record.getRelatedId(), record.getPaymentMethod(), record.getTransactionId(), record.getPaymentTime(), record.getRemark());
        }
        
        return paymentRecords;
    }
    
    /**
     * 🔧 新增：通过充电服务获取订单的数据库ID
     */
    private Long getChargingOrderDbId(String orderId) {
        try {
            log.info("🔍 [获取订单ID] 通过充电服务获取订单{}的数据库ID", orderId);
            
            RestTemplate restTemplate = new RestTemplate();
            
            // 🔧 优先使用localhost（开发环境兼容）
            String chargerServiceUrl = "http://localhost:8084/charger/api/charger/order/no/" + orderId;
            
            log.info("📞 [获取订单ID] 调用充电服务: {}", chargerServiceUrl);
            ResponseEntity<Map> response = restTemplate.getForEntity(chargerServiceUrl, Map.class);
            
            log.info("📞 [获取订单ID] 充电服务响应状态: {}", response.getStatusCode());
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseBody = response.getBody();
                
                log.info("📞 [获取订单ID] 充电服务响应内容: {}", responseBody);
                
                // 检查响应格式
                if (responseBody.containsKey("code") && responseBody.containsKey("data")) {
                    Integer code = (Integer) responseBody.get("code");
                    if (code == 200) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> orderData = (Map<String, Object>) responseBody.get("data");
                        
                        if (orderData != null && orderData.containsKey("id")) {
                            Object orderIdObj = orderData.get("id");
                            if (orderIdObj instanceof Number) {
                                Long orderDbId = ((Number) orderIdObj).longValue();
                                log.info("✅ [获取订单ID] 成功获取订单{}的数据库ID: {}", orderId, orderDbId);
                                return orderDbId;
                            } else {
                                log.warn("⚠️ [获取订单ID] 订单ID字段类型异常: {}", orderIdObj);
                            }
                        } else {
                            log.warn("⚠️ [获取订单ID] 订单数据中没有id字段，orderData: {}", orderData);
                        }
                    } else {
                        String message = (String) responseBody.get("message");
                        log.warn("⚠️ [获取订单ID] 充电服务返回错误：code={}, message={}", code, message);
                    }
                } else {
                    log.warn("⚠️ [获取订单ID] 充电服务响应格式异常，缺少code或data字段: {}", responseBody);
                }
            } else {
                log.warn("⚠️ [获取订单ID] 充电服务调用失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
            }
            
        } catch (Exception e) {
            log.error("❌ [获取订单ID] 调用充电服务获取订单{}数据库ID异常: {}", orderId, e.getMessage());
            
            // 🔧 如果localhost调用失败，尝试使用服务名（微服务环境）
            try {
                log.info("🔍 [获取订单ID] 尝试使用服务名调用充电服务（微服务环境）");
                RestTemplate restTemplate = new RestTemplate();
                String fallbackUrl = "http://service-charger/charger/api/charger/order/no/" + orderId;
                
                log.info("📞 [获取订单ID] 备用调用充电服务: {}", fallbackUrl);
                ResponseEntity<Map> fallbackResponse = restTemplate.getForEntity(fallbackUrl, Map.class);
                
                if (fallbackResponse.getStatusCode().is2xxSuccessful() && fallbackResponse.getBody() != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> responseBody = fallbackResponse.getBody();
                    
                    log.info("📞 [获取订单ID] 备用调用充电服务响应: {}", responseBody);
                    
                    if (responseBody.containsKey("code") && responseBody.containsKey("data")) {
                        Integer code = (Integer) responseBody.get("code");
                        if (code == 200) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> orderData = (Map<String, Object>) responseBody.get("data");
                            
                            if (orderData != null && orderData.containsKey("id")) {
                                Object orderIdObj = orderData.get("id");
                                if (orderIdObj instanceof Number) {
                                    Long orderDbId = ((Number) orderIdObj).longValue();
                                    log.info("✅ [获取订单ID] 通过备用调用成功获取订单{}的数据库ID: {}", orderId, orderDbId);
                                    return orderDbId;
                                }
                            }
                        }
                    }
                }
            } catch (Exception fallbackException) {
                log.error("❌ [获取订单ID] 备用调用充电服务也失败: {}", fallbackException.getMessage());
            }
        }
        
        log.error("❌ [获取订单ID] 无法获取订单{}的数据库ID", orderId);
        return null;
    }
    
    /**
     * 检查时间戳是否匹配
     */
    private boolean isTimeStampMatch(String paymentNo, String orderTimeStamp) {
        if (paymentNo == null || orderTimeStamp == null || orderTimeStamp.length() < 10) {
            return false;
        }
        
        // 提取订单号中的时间戳前10位进行匹配
        String shortTimeStamp = orderTimeStamp.substring(0, Math.min(10, orderTimeStamp.length()));
        return paymentNo.contains(shortTimeStamp);
    }
    
    /**
     * 🔧 简化版本的用户ID提取方法，避免网络调用失败
     */
    private Long extractUserIdFromOrderSimple(String orderId) {
        try {
            log.info("🔍 [提取用户ID] 尝试从支付记录推断订单{}的用户ID", orderId);

            // 方法1：尝试从相关支付记录查询（通过relatedId）
            List<PaymentRecord> relatedRecords = paymentRecordRepository.findByRelatedId(Long.valueOf(orderId.hashCode()));
            if (!relatedRecords.isEmpty()) {
                Long userId = relatedRecords.get(0).getUserId();
                log.info("🔍 从相关支付记录获取用户ID: {}", userId);
                return userId;
            }

            // 方法2：尝试从最近的充电支付记录推断用户ID
            List<PaymentRecord> recentPayments = paymentRecordRepository.findByRelatedTypeAndStatus("CHARGING", PaymentRecord.STATUS_SUCCESS);
            log.info("📊 [提取用户ID] 查询到{}条成功的充电支付记录", recentPayments.size());

            // 查找最近30分钟内的支付记录
            LocalDateTime thirtyMinutesAgo = LocalDateTime.now().minusMinutes(30);
            for (PaymentRecord record : recentPayments) {
                if (record.getCreateTime() != null && record.getCreateTime().isAfter(thirtyMinutesAgo)) {
                    // 检查备注中是否包含订单相关信息
                    if (record.getRemark() != null && record.getRemark().contains("充电")) {
                        log.info("✅ [提取用户ID] 从最近充电支付记录推断用户ID：订单={}, 用户ID={}, 支付时间={}, 备注={}",
                                orderId, record.getUserId(), record.getCreateTime(), record.getRemark());
                        return record.getUserId();
                    }
                }
            }

            log.warn("⚠️ [提取用户ID] 无法推断订单{}的用户ID", orderId);
            return null;

        } catch (Exception e) {
            log.error("❌ [提取用户ID] 提取用户ID异常，订单：{}", orderId, e);
            return null;
        }
    }
    
    /**
     * 构建退费备注
     */
    private String buildRefundRemark(RefundRequest request) {
        StringBuilder remark = new StringBuilder();
        
        // 添加退费类型
        if (request.getReasonType() != null) {
            switch (request.getReasonType()) {
                case "CHARGING_FAILED":
                    remark.append("充电启动失败退费");
                    break;
                case "CHARGING_ABNORMAL":
                    remark.append("充电异常退费");
                    break;
                case "CHARGING_SETTLEMENT":
                    remark.append("充电完成结算退费");
                    break;
                case "DEVICE_ERROR":
                    remark.append("设备故障退费");
                    break;
                default:
                    remark.append("系统退费");
            }
        } else {
            remark.append("系统退费");
        }
        
        // 添加详细原因
        if (request.getReasonDesc() != null && !request.getReasonDesc().trim().isEmpty()) {
            remark.append("：").append(request.getReasonDesc());
        }
        
        // 添加原支付信息
        if (request.getOriginalPaymentNo() != null) {
            remark.append("；原支付单号：").append(request.getOriginalPaymentNo());
        }
        
        // 添加关联订单信息
        if (request.getRelatedOrderId() != null) {
            remark.append("；关联订单：").append(request.getRelatedOrderId());
        }
        
        return remark.toString();
    }
    
    /**
     * 生成退费单号
     */
    private String generateRefundNo() {
        return "RF" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + 
               String.format("%04d", (int)(Math.random() * 10000));
    }
    
    /**
     * 获取用户当前余额
     */
    private BigDecimal getCurrentUserBalance(Long userId) {
        try {
            // 🔧 修复：从用户服务获取真实的用户余额
            log.info("从用户服务获取用户{}的当前余额", userId);
            
            ResponseEntity<Object> response = userServiceClient.getUserById(userId);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
                Integer code = (Integer) responseBody.get("code");
                
                if (code == 200) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> userData = (Map<String, Object>) responseBody.get("data");
                    
                    if (userData != null && userData.containsKey("balance")) {
                        Object balanceObj = userData.get("balance");
                        if (balanceObj instanceof Number) {
                            BigDecimal balance = BigDecimal.valueOf(((Number) balanceObj).doubleValue());
                            log.info("获取到用户{}的真实余额: {}", userId, balance);
                            return balance;
                        }
                    }
                }
            }
            
            log.warn("无法从用户服务获取用户{}余额，使用默认值0", userId);
            return BigDecimal.ZERO;
            
        } catch (Exception e) {
            log.error("获取用户{}余额失败，使用默认值", userId, e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 更新用户余额
     */
    private boolean updateUserBalance(Long userId, BigDecimal newBalance) {
        try {
            // 🔧 修复：真正调用用户服务更新余额
            // 计算余额变化量（因为是退费，所以是增加余额）
            BigDecimal currentBalance = getCurrentUserBalance(userId);
            BigDecimal refundAmount = newBalance.subtract(currentBalance);
            
            if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("退费金额无效：当前余额={}, 目标余额={}, 变化量={}", currentBalance, newBalance, refundAmount);
                return false;
            }
            
            // 构建更新余额请求
            Map<String, Object> balanceRequest = new HashMap<>();
            balanceRequest.put("userId", userId);
            balanceRequest.put("amount", refundAmount.doubleValue());  // 用户服务期望double类型
            balanceRequest.put("type", "RECHARGE");  // 退费相当于充值，增加余额
            balanceRequest.put("relatedId", null);  // 退费记录的ID会在外部设置
            
            log.info("调用用户服务更新余额：用户ID={}, 退费金额={}, 类型=RECHARGE", userId, refundAmount);
            
            // 调用用户服务
            ResponseEntity<Object> response = userServiceClient.updateBalance(balanceRequest);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
                Integer code = (Integer) responseBody.get("code");
                
                if (code == 200) {
                    log.info("用户{}余额更新成功，新余额：{}", userId, newBalance);
                    return true;
                } else {
                    String message = (String) responseBody.get("message");
                    log.error("用户服务返回错误：code={}, message={}", code, message);
                    return false;
                }
            } else {
                log.error("调用用户服务失败：状态码={}, 响应={}", response.getStatusCode(), response.getBody());
                return false;
            }
            
        } catch (Exception e) {
            log.error("更新用户{}余额失败", userId, e);
            return false;
        }
    }
    
    /**
     * 确定退费方式
     */
    private String determineRefundMethod(String originalPaymentMethod, List<PaymentRecord> paymentRecords, BigDecimal refundAmount, String orderId) {
        log.info("🔧 确定退费方式：原支付方式={}, 支付记录数={}, 退费金额={}, 订单={}", 
                originalPaymentMethod, paymentRecords != null ? paymentRecords.size() : 0, refundAmount, orderId);
        
        if (paymentRecords == null || paymentRecords.isEmpty()) {
            log.info("🔧 没有支付记录，默认余额退费");
            return "余额";
        }
        
        // 使用新的分析方法
        RefundAnalysisResult analysis = analyzePaymentRecordsForRefund(paymentRecords, refundAmount, orderId);
        
        if ("WECHAT_ORIGINAL".equals(analysis.getRefundMethod())) {
            return "微信原路退回";
        } else {
            return "余额";
        }
    }
    
    /**
     * 🔧 新增：分析支付记录确定退款方式和校验退款金额
     */
    private RefundAnalysisResult analyzePaymentRecordsForRefund(List<PaymentRecord> paymentRecords, BigDecimal requestedRefundAmount, String orderId) {
        log.info("🔧 开始分析支付记录确定退款方式：订单ID={}, 请求退款金额={}, 支付记录数量={}", 
                orderId, requestedRefundAmount, paymentRecords.size());
        
        RefundAnalysisResult result = new RefundAnalysisResult();
        result.setOrderId(orderId);
        result.setRequestedRefundAmount(requestedRefundAmount);
        
        if (paymentRecords.isEmpty()) {
            log.warn("⚠️ 订单{}没有找到任何支付记录，可能是余额支付", orderId);
            result.setRefundMethod("BALANCE");
            result.setRefundMethodName("余额退款");
            result.setCanRefund(true);
            result.setValidatedRefundAmount(requestedRefundAmount);
            result.setReason("未找到第三方支付记录，推断为余额支付");
            return result;
        }
        
        // 统计各种支付方式的金额
        BigDecimal totalWechatAmount = BigDecimal.ZERO;
        BigDecimal totalBalanceAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        int wechatPaymentCount = 0;
        int balancePaymentCount = 0;
        
        PaymentRecord latestWechatRecord = null;
        
        // 分析支付记录
        for (PaymentRecord record : paymentRecords) {
            if (record.getStatus().equals(PaymentRecord.STATUS_SUCCESS)) {
                totalAmount = totalAmount.add(record.getAmount());
                
                // 🔧 改进支付方式判断逻辑
                String paymentMethod = record.getPaymentMethod();
                if ("微信支付".equals(paymentMethod) || "wechat".equals(paymentMethod)) {
                    totalWechatAmount = totalWechatAmount.add(record.getAmount());
                    wechatPaymentCount++;
                    // 记录最新的微信支付记录（用于获取微信订单号）
                    if (latestWechatRecord == null || record.getCreateTime().isAfter(latestWechatRecord.getCreateTime())) {
                        latestWechatRecord = record;
                    }
                } else if ("余额支付".equals(paymentMethod) || "balance".equals(paymentMethod)) {
                    totalBalanceAmount = totalBalanceAmount.add(record.getAmount());
                    balancePaymentCount++;
                }
                
                log.debug("🔍 支付记录分析：ID={}, 支付方式={}, 金额={}, 状态={}", 
                        record.getId(), paymentMethod, record.getAmount(), record.getStatus());
            }
        }
        
        log.info("📊 支付分析结果：总金额={}, 微信支付={}, 余额支付={}, 微信记录数={}, 余额记录数={}", 
                totalAmount, totalWechatAmount, totalBalanceAmount, wechatPaymentCount, balancePaymentCount);
        
        // 校验退款金额不能超过总支付金额
        if (requestedRefundAmount.compareTo(totalAmount) > 0) {
            log.error("❌ 退款金额{}超过总支付金额{}，拒绝退款", requestedRefundAmount, totalAmount);
            result.setCanRefund(false);
            result.setReason(String.format("退款金额%.2f元超过总支付金额%.2f元", 
                    requestedRefundAmount.doubleValue(), totalAmount.doubleValue()));
            return result;
        }
        
        // 🔧 改进退款策略判断：只有当余额支付金额大于0时才认为是混合支付
        if (wechatPaymentCount > 0 && balancePaymentCount > 0 && totalBalanceAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 🔧 真正的混合支付：既有微信支付又有余额支付，且余额支付金额大于0
            log.info("🔧 检测到混合支付（微信{}元 + 余额{}元），退款到余额", totalWechatAmount, totalBalanceAmount);
            result.setRefundMethod("BALANCE");
            result.setRefundMethodName("余额退款");
            result.setCanRefund(true);
            result.setValidatedRefundAmount(requestedRefundAmount);
            result.setReason("混合支付，统一退到余额");
            
        } else if (wechatPaymentCount > 0 && (balancePaymentCount == 0 || totalBalanceAmount.compareTo(BigDecimal.ZERO) == 0)) {
            // 🔧 纯微信支付：只有微信支付记录，或者余额支付金额为0
            log.info("🔧 检测到纯微信支付（{}元），准备原路退回", totalWechatAmount);
            
            // 校验退款金额不能超过微信支付金额
            if (requestedRefundAmount.compareTo(totalWechatAmount) > 0) {
                log.error("❌ 退款金额{}超过微信支付金额{}，拒绝原路退回", requestedRefundAmount, totalWechatAmount);
                result.setCanRefund(false);
                result.setReason(String.format("退款金额%.2f元超过微信支付金额%.2f元", 
                        requestedRefundAmount.doubleValue(), totalWechatAmount.doubleValue()));
                return result;
            }
            
            if (latestWechatRecord == null || latestWechatRecord.getTransactionId() == null || latestWechatRecord.getTransactionId().trim().isEmpty()) {
                log.warn("⚠️ 纯微信支付但缺少微信订单号，改为余额退款");
                result.setRefundMethod("BALANCE");
                result.setRefundMethodName("余额退款");
                result.setCanRefund(true);
                result.setValidatedRefundAmount(requestedRefundAmount);
                result.setReason("纯微信支付但缺少微信订单号，改为余额退款");
            } else {
                result.setRefundMethod("WECHAT_ORIGINAL");
                result.setRefundMethodName("微信原路退回");
                result.setCanRefund(true);
                result.setValidatedRefundAmount(requestedRefundAmount);
                result.setWechatTransactionId(latestWechatRecord.getTransactionId());
                result.setOriginalWechatAmount(totalWechatAmount);
                result.setWechatPaymentRecord(latestWechatRecord);
                result.setReason("纯微信支付，满足原路退回条件");
                log.info("✅ 原路退回参数：微信订单号={}, 原支付金额={}, 退款金额={}", 
                        result.getWechatTransactionId(), result.getOriginalWechatAmount(), result.getValidatedRefundAmount());
            }
            
        } else if (balancePaymentCount > 0 && wechatPaymentCount == 0) {
            // 🔧 纯余额支付：只有余额支付
            log.info("🔧 检测到纯余额支付（{}元），退款到余额", totalBalanceAmount);
            result.setRefundMethod("BALANCE");
            result.setRefundMethodName("余额退款");
            result.setCanRefund(true);
            result.setValidatedRefundAmount(requestedRefundAmount);
            result.setReason("纯余额支付，退到余额");
            
        } else {
            // 没有有效的支付记录
            log.warn("⚠️ 没有找到有效的支付记录，默认余额退款");
            result.setRefundMethod("BALANCE");
            result.setRefundMethodName("余额退款");
            result.setCanRefund(true);
            result.setValidatedRefundAmount(requestedRefundAmount);
            result.setReason("没有有效支付记录，默认余额退款");
        }
        
        return result;
    }
    
    /**
     * 🔧 新增：执行微信原路退款
     */
    private boolean processWechatOriginalRefund(RefundAnalysisResult analysis, String refundNo) {
        log.info("🔧 [微信原路退款] 开始执行微信原路退款：订单={}, 退款单号={}, 微信订单号={}, 原金额={}, 退款金额={}", 
                analysis.getOrderId(), refundNo, analysis.getWechatTransactionId(), 
                analysis.getOriginalWechatAmount(), analysis.getValidatedRefundAmount());
        
        try {
            log.info("📞 [微信原路退款] 准备调用微信退款API...");
            log.info("📋 [微信原路退款] 退款参数：微信订单号={}, 退款单号={}, 原金额={}, 退款金额={}", 
                    analysis.getWechatTransactionId(), refundNo, 
                    analysis.getOriginalWechatAmount(), analysis.getValidatedRefundAmount());
            
            // 调用微信退款API
            Object refundResult = wechatPayService.refund(
                    analysis.getWechatTransactionId(),  // 微信订单号
                    refundNo,                           // 退款单号
                    analysis.getOriginalWechatAmount(), // 原订单总金额
                    analysis.getValidatedRefundAmount(), // 退款金额
                    "充电订单退款 - " + analysis.getReason()  // 退款原因
            );
            
            log.info("✅ [微信原路退款] 微信退款API调用成功：订单={}, 退款单号={}", 
                    analysis.getOrderId(), refundNo);
            log.info("📄 [微信原路退款] API响应详情：{}", refundResult.toString());
            
            // 解析微信退款响应
            if (refundResult instanceof JSONObject) {
                JSONObject refundJson = (JSONObject) refundResult;
                String status = refundJson.getString("status");
                String refundId = refundJson.getString("refund_id");
                String successTime = refundJson.getString("success_time");
                
                log.info("📊 [微信原路退款] 微信退款详情：状态={}, 微信退款单号={}, 退款到账时间={}", 
                        status, refundId, successTime);
                
                // 通常微信退款是异步处理，这里返回true表示提交成功
                // 最终状态需要通过退款回调确认
                boolean isSuccess = "SUCCESS".equals(status) || "PROCESSING".equals(status);
                
                if (isSuccess) {
                    log.info("🎉 [微信原路退款] 微信退款提交成功，状态：{}", status);
                } else {
                    log.warn("⚠️ [微信原路退款] 微信退款状态异常：{}", status);
                }
                
                return isSuccess;
            }
            
            log.warn("⚠️ [微信原路退款] 微信退款响应格式异常：{}", refundResult);
            return false;
            
        } catch (Exception e) {
            log.error("❌ [微信原路退款] 微信原路退款执行失败：订单={}, 退款单号={}", 
                    analysis.getOrderId(), refundNo, e);
            
            // 微信原路退款失败，记录详细错误信息
            log.error("🔍 [微信原路退款] 错误详情：微信订单号={}, 退款金额={}, 错误消息={}", 
                    analysis.getWechatTransactionId(), analysis.getValidatedRefundAmount(), e.getMessage());
            
            return false;
        }
    }
    
    /**
     * 确定原支付方式
     */
    private String determineOriginalPaymentMethod(List<PaymentRecord> paymentRecords) {
        if (paymentRecords.isEmpty()) {
            return "unknown";
        }
        
        // 如果有多种支付方式，优先显示微信支付
        for (PaymentRecord record : paymentRecords) {
            if ("微信支付".equals(record.getPaymentMethod())) {
                return "微信支付";
            }
        }
        
        return paymentRecords.get(0).getPaymentMethod();
    }
    
    /**
     * 从订单号推断用户ID
     * 通过调用充电服务获取订单详情
     */
    private Long extractUserIdFromOrder(String orderId) {
        try {
            log.info("🔍 [获取用户ID] 尝试从充电服务获取订单{}的用户ID", orderId);
            
            // 🔧 方法1：使用服务名调用充电服务API
            try {
                RestTemplate restTemplate = new RestTemplate();
                // 使用服务名而不是localhost，支持微服务环境
                String chargerServiceUrl = "http://service-charger/charger/api/charger/order/no/" + orderId;
                
                log.info("📞 [获取用户ID] 调用充电服务: {}", chargerServiceUrl);
                ResponseEntity<Map> response = restTemplate.getForEntity(chargerServiceUrl, Map.class);
                
                log.info("📞 [获取用户ID] 充电服务响应状态: {}", response.getStatusCode());
                
                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> responseBody = response.getBody();
                    
                    log.info("📞 [获取用户ID] 充电服务响应内容: {}", responseBody);
                    
                    // 检查响应格式
                    if (responseBody.containsKey("code") && responseBody.containsKey("data")) {
                        Integer code = (Integer) responseBody.get("code");
                        if (code == 200) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> orderData = (Map<String, Object>) responseBody.get("data");
                            
                            if (orderData != null && orderData.containsKey("userId")) {
                                Object userIdObj = orderData.get("userId");
                                if (userIdObj instanceof Number) {
                                    Long userId = ((Number) userIdObj).longValue();
                                    log.info("✅ [获取用户ID] 成功从充电服务获取订单{}的用户ID: {}", orderId, userId);
                                    return userId;
                                } else {
                                    log.warn("⚠️ [获取用户ID] 用户ID字段类型异常: {}", userIdObj);
                                }
                            } else {
                                log.warn("⚠️ [获取用户ID] 订单数据中没有userId字段，orderData: {}", orderData);
                            }
                        } else {
                            String message = (String) responseBody.get("message");
                            log.warn("⚠️ [获取用户ID] 充电服务返回错误：code={}, message={}", code, message);
                        }
                    } else {
                        log.warn("⚠️ [获取用户ID] 充电服务响应格式异常，缺少code或data字段: {}", responseBody);
                    }
                } else {
                    log.warn("⚠️ [获取用户ID] 充电服务调用失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                }
                
            } catch (Exception e) {
                log.error("❌ [获取用户ID] 调用充电服务获取订单{}信息异常: {}", orderId, e.getMessage());
                
                // 🔧 方法2：如果服务名调用失败，尝试使用localhost（开发环境兼容）
                try {
                    log.info("🔍 [获取用户ID] 尝试使用localhost调用充电服务（开发环境兼容）");
                    RestTemplate restTemplate = new RestTemplate();
                    String fallbackUrl = "http://localhost:8084/charger/api/charger/order/no/" + orderId;
                    
                    log.info("📞 [获取用户ID] 备用调用充电服务: {}", fallbackUrl);
                    ResponseEntity<Map> fallbackResponse = restTemplate.getForEntity(fallbackUrl, Map.class);
                    
                    if (fallbackResponse.getStatusCode().is2xxSuccessful() && fallbackResponse.getBody() != null) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> responseBody = fallbackResponse.getBody();
                        
                        log.info("📞 [获取用户ID] 备用调用充电服务响应: {}", responseBody);
                        
                        if (responseBody.containsKey("code") && responseBody.containsKey("data")) {
                            Integer code = (Integer) responseBody.get("code");
                            if (code == 200) {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> orderData = (Map<String, Object>) responseBody.get("data");
                                
                                if (orderData != null && orderData.containsKey("userId")) {
                                    Object userIdObj = orderData.get("userId");
                                    if (userIdObj instanceof Number) {
                                        Long userId = ((Number) userIdObj).longValue();
                                        log.info("✅ [获取用户ID] 通过备用调用成功获取订单{}的用户ID: {}", orderId, userId);
                                        return userId;
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception fallbackException) {
                    log.error("❌ [获取用户ID] 备用调用充电服务也失败: {}", fallbackException.getMessage());
                }
            }
            
            // 🔧 方法3：如果调用充电服务失败，尝试从支付记录中推断用户ID
            log.info("🔍 [获取用户ID] 方法3：尝试从最近的支付记录推断用户ID");
            List<PaymentRecord> recentPayments = paymentRecordRepository.findByRelatedTypeAndStatus("CHARGING", PaymentRecord.STATUS_SUCCESS);
            
            // 查找最近10分钟内的支付记录，按时间倒序
            LocalDateTime tenMinutesAgo = LocalDateTime.now().minusMinutes(10);
            List<PaymentRecord> candidateRecords = new ArrayList<>();
            
            for (PaymentRecord record : recentPayments) {
                if (record.getCreateTime() != null && record.getCreateTime().isAfter(tenMinutesAgo)) {
                    // 检查备注中是否包含订单信息或充电关键字
                    if (record.getRemark() != null && 
                        (record.getRemark().contains("充电") || record.getRemark().contains(orderId))) {
                        candidateRecords.add(record);
                    }
                }
            }
            
            if (!candidateRecords.isEmpty()) {
                // 按时间倒序排序，取最新的一条
                candidateRecords.sort((r1, r2) -> {
                    if (r1.getCreateTime() == null && r2.getCreateTime() == null) return 0;
                    if (r1.getCreateTime() == null) return 1;
                    if (r2.getCreateTime() == null) return -1;
                    return r2.getCreateTime().compareTo(r1.getCreateTime());
                });
                
                PaymentRecord latestRecord = candidateRecords.get(0);
                log.info("✅ [获取用户ID] 从支付记录推断用户ID: 订单={}, 用户ID={}, 支付时间={}, 备注={}", 
                        orderId, latestRecord.getUserId(), latestRecord.getCreateTime(), latestRecord.getRemark());
                return latestRecord.getUserId();
            }
            
            log.error("❌ [获取用户ID] 所有方法都失败，无法确定订单{}的用户ID", orderId);
            return null;
            
        } catch (Exception e) {
            log.error("❌ [获取用户ID] 从订单号{}推断用户ID失败", orderId, e);
            return null;
        }
    }
    
    /**
     * 从订单计算退费金额
     * 优先从支付记录中查找实际支付金额，如果找不到则使用默认值
     */
    private BigDecimal calculateRefundAmountFromOrder(String orderId) {
        try {
            log.info("尝试从订单{}计算退费金额", orderId);
            
            // 首先尝试查找最近的支付记录来确定实际支付金额
            List<PaymentRecord> recentPayments = paymentRecordRepository.findByRelatedTypeAndStatus("CHARGING", PaymentRecord.STATUS_SUCCESS);
            
            // 按时间倒序排列，查找最近5分钟内的支付记录
            LocalDateTime fiveMinutesAgo = LocalDateTime.now().minusMinutes(5);
            BigDecimal totalPaidAmount = BigDecimal.ZERO;
            
            for (PaymentRecord record : recentPayments) {
                if (record.getCreateTime() != null && record.getCreateTime().isAfter(fiveMinutesAgo)) {
                    // 检查备注中是否包含相关信息，或者是最近的支付记录
                    if (record.getRemark() != null && record.getRemark().contains("充电")) {
                        totalPaidAmount = totalPaidAmount.add(record.getAmount());
                        log.info("找到最近的充电支付记录：金额={}, 时间={}", record.getAmount(), record.getCreateTime());
                    }
                }
            }
            
            // 如果找到了实际支付金额，使用实际金额
            if (totalPaidAmount.compareTo(BigDecimal.ZERO) > 0) {
                log.info("根据支付记录计算退费金额：{}", totalPaidAmount);
                return totalPaidAmount;
            }
            
            // 如果没有找到支付记录，尝试从订单服务获取信息
            // TODO: 实现通过OrderServiceClient获取订单详情
            
            // 作为最后的备选方案，使用较小的默认值
            // 避免过度退费，如果确实需要更多退费，应该通过人工处理
            BigDecimal defaultAmount = new BigDecimal("0.01");
            log.warn("无法确定订单{}的实际支付金额，使用默认退费金额：{}", orderId, defaultAmount);
            return defaultAmount;
            
        } catch (Exception e) {
            log.error("从订单{}计算退费金额失败", orderId, e);
            return new BigDecimal("0.01"); // 返回最小退费金额
        }
    }

    /**
     * 🔧 新增：退款分析结果类
     */
    private static class RefundAnalysisResult {
        private String orderId;
        private BigDecimal requestedRefundAmount;
        private boolean canRefund;
        private String refundMethod; // WECHAT_ORIGINAL, BALANCE
        private String refundMethodName; // 微信原路退回, 余额退款
        private BigDecimal validatedRefundAmount;
        private String reason;
        
        // 微信原路退回相关信息
        private String wechatTransactionId;
        private BigDecimal originalWechatAmount;
        private PaymentRecord wechatPaymentRecord;
        
        // Getters and Setters
        public String getOrderId() { return orderId; }
        public void setOrderId(String orderId) { this.orderId = orderId; }
        
        public BigDecimal getRequestedRefundAmount() { return requestedRefundAmount; }
        public void setRequestedRefundAmount(BigDecimal requestedRefundAmount) { this.requestedRefundAmount = requestedRefundAmount; }
        
        public boolean isCanRefund() { return canRefund; }
        public void setCanRefund(boolean canRefund) { this.canRefund = canRefund; }
        
        public String getRefundMethod() { return refundMethod; }
        public void setRefundMethod(String refundMethod) { this.refundMethod = refundMethod; }
        
        public String getRefundMethodName() { return refundMethodName; }
        public void setRefundMethodName(String refundMethodName) { this.refundMethodName = refundMethodName; }
        
        public BigDecimal getValidatedRefundAmount() { return validatedRefundAmount; }
        public void setValidatedRefundAmount(BigDecimal validatedRefundAmount) { this.validatedRefundAmount = validatedRefundAmount; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        
        public String getWechatTransactionId() { return wechatTransactionId; }
        public void setWechatTransactionId(String wechatTransactionId) { this.wechatTransactionId = wechatTransactionId; }
        
        public BigDecimal getOriginalWechatAmount() { return originalWechatAmount; }
        public void setOriginalWechatAmount(BigDecimal originalWechatAmount) { this.originalWechatAmount = originalWechatAmount; }
        
        public PaymentRecord getWechatPaymentRecord() { return wechatPaymentRecord; }
        public void setWechatPaymentRecord(PaymentRecord wechatPaymentRecord) { this.wechatPaymentRecord = wechatPaymentRecord; }
    }

    /**
     * 🔧 新增：查找用户最近的支付记录（用于USER_开头的订单ID）
     */
    private List<PaymentRecord> findUserRecentPaymentRecords(Long userId) {
        try {
            log.info("🔍 [查找用户支付记录] 查找用户{}最近的支付记录", userId);

            // 查找该用户最近10分钟内的充电支付记录
            LocalDateTime tenMinutesAgo = LocalDateTime.now().minusMinutes(10);

            List<PaymentRecord> recentRecords = paymentRecordRepository.findByUserIdAndRelatedTypeOrderByCreateTimeDesc(userId, "CHARGING");

            // 🔧 关键修改：不再只查找成功状态，也包括待支付状态(status=3)
            // 因为微信支付创建后状态是3，用户可能还没完成支付
            List<PaymentRecord> filteredRecords = recentRecords.stream()
                .filter(record -> record.getCreateTime() != null && record.getCreateTime().isAfter(tenMinutesAgo))
                .filter(record -> {
                    // 包括成功(1)和待支付(3)状态的记录
                    Integer status = record.getStatus();
                    return status != null && (status.equals(PaymentRecord.STATUS_SUCCESS) || status.equals(3));
                })
                .collect(java.util.stream.Collectors.toList());

            log.info("🔍 [查找用户支付记录] 用户{}最近10分钟内有{}条充电支付记录(包括待支付)", userId, filteredRecords.size());

            // 打印找到的记录详情
            for (PaymentRecord record : filteredRecords) {
                log.info("🔍 [支付记录详情] ID={}, 金额={}, 状态={}, 备注={}",
                    record.getId(), record.getAmount(), record.getStatus(),
                    record.getRemark() != null ? record.getRemark().substring(0, Math.min(100, record.getRemark().length())) : "无");
            }

            return filteredRecords;
        } catch (Exception e) {
            log.error("❌ [查找用户支付记录] 查找用户{}最近支付记录失败", userId, e);
            return new ArrayList<>();
        }
    }

}
