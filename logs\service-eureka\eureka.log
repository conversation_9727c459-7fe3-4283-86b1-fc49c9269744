2025-07-30 09:22:02.180 [main] INFO  c.e.eureka.EurekaServerApplication - Starting EurekaServerApplication v0.0.1-SNAPSHOT using Java 24.0.1 with PID 49892 (G:\work\mobile\server\service-eureka\target\service-eureka-0.0.1-SNAPSHOT.jar started by Administrator in G:\work\mobile)
2025-07-30 09:22:02.191 [main] INFO  c.e.eureka.EurekaServerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:22:03.067 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=e463bf49-19d3-338a-a653-9324d90ef40e
2025-07-30 09:22:03.123 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-30 09:22:03.126 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-30 09:22:03.402 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8761 (http)
2025-07-30 09:22:03.411 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8761"]
2025-07-30 09:22:03.413 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:22:03.413 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-30 09:22:03.441 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:22:03.442 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1197 ms
2025-07-30 09:22:04.196 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-07-30 09:22:04.197 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-07-30 09:22:04.375 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-07-30 09:22:04.375 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-07-30 09:22:04.466 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 3329a4f8-7ba7-4a14-a276-00ea5f663ffc

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-30 09:22:04.760 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-30 09:22:05.241 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-30 09:22:05.254 [main] INFO  o.s.c.n.eureka.InstanceInfoFactory - Setting initial instance status as: STARTING
2025-07-30 09:22:05.268 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-07-30 09:22:05.269 [main] INFO  c.netflix.discovery.DiscoveryClient - Client configured to neither register nor query for data.
2025-07-30 09:22:05.274 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1753838525273 with initial instances count: 0
2025-07-30 09:22:05.296 [main] INFO  c.n.e.DefaultEurekaServerContext - Initializing ...
2025-07-30 09:22:05.298 [main] INFO  c.n.eureka.cluster.PeerEurekaNodes - Adding new peer nodes [http://localhost:8761/eureka/]
2025-07-30 09:22:05.438 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-07-30 09:22:05.439 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-07-30 09:22:05.440 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-07-30 09:22:05.440 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-07-30 09:22:05.490 [main] INFO  c.n.eureka.cluster.PeerEurekaNodes - Replica node URL:  http://localhost:8761/eureka/
2025-07-30 09:22:05.495 [main] INFO  c.n.e.r.AbstractInstanceRegistry - Finished initializing remote region registries. All known remote regions: []
2025-07-30 09:22:05.496 [main] INFO  c.n.e.DefaultEurekaServerContext - Initialized
2025-07-30 09:22:05.546 [main] INFO  o.s.c.n.e.s.EurekaServiceRegistry - Registering application SERVICE-EUREKA with eureka with status UP
2025-07-30 09:22:05.547 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8761"]
2025-07-30 09:22:05.553 [Thread-9] INFO  o.s.c.n.e.s.EurekaServerBootstrap - isAws returned false
2025-07-30 09:22:05.553 [Thread-9] INFO  o.s.c.n.e.s.EurekaServerBootstrap - Initialized server context
2025-07-30 09:22:05.553 [Thread-9] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Got 1 instances from neighboring DS node
2025-07-30 09:22:05.553 [Thread-9] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Renew threshold is: 1
2025-07-30 09:22:05.554 [Thread-9] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Changing status to UP
2025-07-30 09:22:05.557 [Thread-9] INFO  o.s.c.n.e.s.EurekaServerInitializerConfiguration - Started Eureka Server
2025-07-30 09:22:05.586 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8761 (http) with context path ''
2025-07-30 09:22:05.587 [main] INFO  o.s.c.n.e.s.EurekaAutoServiceRegistration - Updating port to 8761
2025-07-30 09:22:05.598 [main] INFO  c.e.eureka.EurekaServerApplication - Started EurekaServerApplication in 4.583 seconds (process running for 5.572)
2025-07-30 09:22:24.368 [http-nio-8761-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:22:24.369 [http-nio-8761-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:22:24.371 [http-nio-8761-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-30 09:22:24.599 [http-nio-8761-exec-2] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CONFIG/host.docker.internal:service-config:8888 with status UP (replication=false)
2025-07-30 09:22:25.188 [http-nio-8761-exec-5] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CONFIG/host.docker.internal:service-config:8888 with status UP (replication=true)
2025-07-30 09:22:41.917 [http-nio-8761-exec-7] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-GATEWAY/host.docker.internal:service-gateway:8090 with status UP (replication=false)
2025-07-30 09:22:42.427 [http-nio-8761-exec-8] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-GATEWAY/host.docker.internal:service-gateway:8090 with status UP (replication=true)
2025-07-30 09:22:57.465 [http-nio-8761-exec-8] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-AUTH/host.docker.internal:service-auth:8081 with status UP (replication=false)
2025-07-30 09:22:57.974 [http-nio-8761-exec-9] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-AUTH/host.docker.internal:service-auth:8081 with status UP (replication=true)
2025-07-30 09:23:05.554 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 09:23:13.297 [http-nio-8761-exec-8] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:23:25.936 [http-nio-8761-exec-1] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-REPORT/host.docker.internal:service-report:8087 with status UP (replication=false)
2025-07-30 09:23:26.469 [http-nio-8761-exec-2] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-REPORT/host.docker.internal:service-report:8087 with status UP (replication=true)
2025-07-30 09:23:30.005 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:23:34.297 [http-nio-8761-exec-10] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-USER/host.docker.internal:service-user:8082 with status UP (replication=false)
2025-07-30 09:23:34.530 [http-nio-8761-exec-1] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 with status UP (replication=false)
2025-07-30 09:23:34.811 [http-nio-8761-exec-4] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-USER/host.docker.internal:service-user:8082 with status UP (replication=true)
2025-07-30 09:23:34.813 [http-nio-8761-exec-4] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 with status UP (replication=true)
2025-07-30 09:23:34.960 [http-nio-8761-exec-6] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-ORDER/host.docker.internal:service-order:8085 with status UP (replication=false)
2025-07-30 09:23:35.137 [http-nio-8761-exec-8] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-VEHICLE/host.docker.internal:service-vehicle:8086 with status UP (replication=false)
2025-07-30 09:23:35.470 [http-nio-8761-exec-10] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-ORDER/host.docker.internal:service-order:8085 with status UP (replication=true)
2025-07-30 09:23:35.471 [http-nio-8761-exec-10] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-VEHICLE/host.docker.internal:service-vehicle:8086 with status UP (replication=true)
2025-07-30 09:23:35.637 [http-nio-8761-exec-5] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-PAYMENT/host.docker.internal:service-payment:8089 with status UP (replication=false)
2025-07-30 09:23:36.144 [http-nio-8761-exec-4] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-PAYMENT/host.docker.internal:service-payment:8089 with status UP (replication=true)
2025-07-30 09:24:00.005 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:24:05.555 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 09:25:05.564 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 09:26:05.571 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 09:27:00.136 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:27:05.582 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 10ms
2025-07-30 09:28:05.592 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 10ms
2025-07-30 09:29:05.595 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 2ms
2025-07-30 09:30:00.171 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:30:05.601 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 09:31:05.604 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 2ms
2025-07-30 09:32:05.613 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 09:33:00.198 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:33:05.628 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 15ms
2025-07-30 09:34:05.643 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 14ms
2025-07-30 09:35:05.652 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 8ms
2025-07-30 09:36:00.239 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:36:05.661 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 09:37:05.496 [ReplicaAwareInstanceRegistry - RenewalThresholdUpdater] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Current renewal threshold is : 0
2025-07-30 09:37:05.667 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 09:38:05.680 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 09:39:00.290 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:39:05.687 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 09:40:05.690 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 09:41:05.705 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 14ms
2025-07-30 09:42:00.337 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:42:05.717 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 09:43:05.725 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 8ms
2025-07-30 09:44:05.731 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 5ms
2025-07-30 09:45:00.385 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:45:05.746 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 14ms
2025-07-30 09:46:05.753 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 09:47:05.754 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 09:48:00.436 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:48:05.757 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 09:49:05.768 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 10ms
2025-07-30 09:50:05.777 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 09:51:00.490 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:51:05.781 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 09:52:05.509 [ReplicaAwareInstanceRegistry - RenewalThresholdUpdater] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Current renewal threshold is : 0
2025-07-30 09:52:05.786 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 09:53:05.791 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 5ms
2025-07-30 09:54:00.527 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:54:05.800 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 8ms
2025-07-30 09:55:05.812 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 09:55:05.813 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Evicting 1 items (expired=1, evictionLimit=2)
2025-07-30 09:55:05.814 [Eureka-EvictionTimer] WARN  c.n.e.r.AbstractInstanceRegistry - DS: Registry: expired lease for SERVICE-CHARGER/host.docker.internal:service-charger:8084
2025-07-30 09:55:05.816 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Cancelled instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 (replication=false)
2025-07-30 09:55:30.545 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:56:05.817 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 09:57:05.832 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 15ms
2025-07-30 09:58:05.846 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 14ms
2025-07-30 09:58:30.592 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 09:59:05.854 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 10:00:04.627 [http-nio-8761-exec-8] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 with status UP (replication=false)
2025-07-30 10:00:05.145 [http-nio-8761-exec-5] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 with status UP (replication=true)
2025-07-30 10:00:05.862 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 10:00:30.628 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:01:05.876 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 14ms
2025-07-30 10:02:05.877 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 1ms
2025-07-30 10:03:05.883 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 5ms
2025-07-30 10:03:30.692 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:04:05.890 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 10:05:05.894 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 10:05:42.482 [http-nio-8761-exec-7] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-FEE/host.docker.internal:service-fee:8083 with status UP (replication=false)
2025-07-30 10:05:42.993 [http-nio-8761-exec-6] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-FEE/host.docker.internal:service-fee:8083 with status UP (replication=true)
2025-07-30 10:06:00.727 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:06:05.896 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 1ms
2025-07-30 10:07:05.520 [ReplicaAwareInstanceRegistry - RenewalThresholdUpdater] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Current renewal threshold is : 0
2025-07-30 10:07:05.910 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 10:08:05.912 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 2ms
2025-07-30 10:09:00.770 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:09:05.924 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 11ms
2025-07-30 10:10:05.929 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 10:11:05.933 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 10:12:00.811 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:12:05.938 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 5ms
2025-07-30 10:13:05.945 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 10:14:05.956 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 11ms
2025-07-30 10:15:00.849 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:15:05.959 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 10:16:05.962 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 2ms
2025-07-30 10:17:05.967 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 5ms
2025-07-30 10:18:00.903 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:18:05.968 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 10:19:05.981 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 10:20:05.985 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 10:21:00.954 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:21:05.994 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 10:22:05.527 [ReplicaAwareInstanceRegistry - RenewalThresholdUpdater] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Current renewal threshold is : 0
2025-07-30 10:22:06.007 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 10:23:06.015 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 10:24:01.019 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:24:06.017 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 1ms
2025-07-30 10:25:06.020 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 2ms
2025-07-30 10:26:06.033 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 10:27:01.051 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:27:06.040 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 10:28:06.049 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 8ms
2025-07-30 10:29:06.059 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 10:30:01.095 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:30:06.061 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 1ms
2025-07-30 10:31:06.075 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 14ms
2025-07-30 10:32:06.078 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 10:33:01.141 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:33:06.085 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 10:34:06.094 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 10:35:06.107 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 10:36:01.187 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:36:06.118 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 10ms
2025-07-30 10:37:05.534 [ReplicaAwareInstanceRegistry - RenewalThresholdUpdater] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Current renewal threshold is : 0
2025-07-30 10:37:06.118 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 10:38:06.123 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 5ms
2025-07-30 10:39:01.238 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:39:06.135 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 10:40:06.144 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 8ms
2025-07-30 10:41:06.153 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 10:42:01.302 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:42:06.156 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 10:43:06.163 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 10:44:06.174 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 11ms
2025-07-30 10:45:01.371 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:45:06.176 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 2ms
2025-07-30 10:46:06.189 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 10:47:06.196 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 10:48:01.413 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:48:06.209 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 10:49:06.223 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 14ms
2025-07-30 10:50:06.224 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 1ms
2025-07-30 10:51:01.457 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:51:06.227 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 10:52:05.546 [ReplicaAwareInstanceRegistry - RenewalThresholdUpdater] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Current renewal threshold is : 0
2025-07-30 10:52:06.228 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 10:53:06.228 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 10:54:01.490 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:54:06.232 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 10:55:06.234 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 1ms
2025-07-30 10:55:06.242 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Evicting 1 items (expired=1, evictionLimit=2)
2025-07-30 10:55:06.242 [Eureka-EvictionTimer] WARN  c.n.e.r.AbstractInstanceRegistry - DS: Registry: expired lease for SERVICE-CHARGER/host.docker.internal:service-charger:8084
2025-07-30 10:55:06.243 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Cancelled instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 (replication=false)
2025-07-30 10:55:31.520 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:56:06.246 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 11ms
2025-07-30 10:57:06.254 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 8ms
2025-07-30 10:58:06.267 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 10:58:31.575 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 10:59:06.281 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 11:00:06.294 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 11:01:06.309 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 14ms
2025-07-30 11:01:31.618 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:02:06.316 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 11:02:06.317 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Evicting 1 items (expired=1, evictionLimit=2)
2025-07-30 11:02:06.326 [Eureka-EvictionTimer] WARN  c.n.e.r.AbstractInstanceRegistry - DS: Registry: expired lease for SERVICE-PAYMENT/host.docker.internal:service-payment:8089
2025-07-30 11:02:06.326 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Cancelled instance SERVICE-PAYMENT/host.docker.internal:service-payment:8089 (replication=false)
2025-07-30 11:02:31.644 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:03:06.327 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 10ms
2025-07-30 11:03:39.159 [http-nio-8761-exec-5] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 with status UP (replication=false)
2025-07-30 11:03:39.679 [http-nio-8761-exec-6] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 with status UP (replication=true)
2025-07-30 11:04:01.665 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:04:06.335 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 11:04:52.945 [http-nio-8761-exec-9] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-PAYMENT/host.docker.internal:service-payment:8089 with status UP (replication=false)
2025-07-30 11:04:53.460 [http-nio-8761-exec-1] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-PAYMENT/host.docker.internal:service-payment:8089 with status UP (replication=true)
2025-07-30 11:05:01.675 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:05:06.350 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 15ms
2025-07-30 11:06:06.358 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 8ms
2025-07-30 11:07:05.556 [ReplicaAwareInstanceRegistry - RenewalThresholdUpdater] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Current renewal threshold is : 0
2025-07-30 11:07:06.418 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 59ms
2025-07-30 11:08:01.720 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:08:06.425 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 11:09:06.433 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 11:10:06.439 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 11:11:01.757 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:11:06.453 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 11:12:06.453 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 11:13:06.458 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 11:14:01.811 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:14:06.462 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 11:15:06.472 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 11:16:06.475 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 11:17:01.865 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:17:06.478 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 11:18:06.491 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 11:19:06.498 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 6ms
2025-07-30 11:20:01.893 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:20:06.511 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 11:21:06.515 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 11:22:05.566 [ReplicaAwareInstanceRegistry - RenewalThresholdUpdater] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Current renewal threshold is : 0
2025-07-30 11:22:06.516 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 11:23:01.935 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:23:06.524 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 7ms
2025-07-30 11:24:06.528 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 11:25:06.534 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 5ms
2025-07-30 11:26:01.988 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:26:06.550 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 15ms
2025-07-30 11:27:06.552 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 2ms
2025-07-30 11:28:06.557 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 4ms
2025-07-30 11:29:02.059 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:29:06.563 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 5ms
2025-07-30 11:30:06.576 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 13ms
2025-07-30 11:31:06.586 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 11:32:02.096 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:32:06.600 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 14ms
2025-07-30 11:33:06.606 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 5ms
2025-07-30 11:34:06.615 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 8ms
2025-07-30 11:35:02.130 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:35:06.627 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 11:36:06.628 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 0ms
2025-07-30 11:37:05.567 [ReplicaAwareInstanceRegistry - RenewalThresholdUpdater] INFO  c.n.e.r.PeerAwareInstanceRegistryImpl - Current renewal threshold is : 0
2025-07-30 11:37:06.638 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 9ms
2025-07-30 11:37:54.298 [http-nio-8761-exec-7] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 with status UP (replication=false)
2025-07-30 11:37:54.585 [http-nio-8761-exec-2] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-CHARGER/host.docker.internal:service-charger:8084 with status UP (replication=true)
2025-07-30 11:38:02.030 [http-nio-8761-exec-7] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-PAYMENT/host.docker.internal:service-payment:8089 with status UP (replication=false)
2025-07-30 11:38:02.177 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:38:02.555 [http-nio-8761-exec-2] INFO  c.n.e.r.AbstractInstanceRegistry - Registered instance SERVICE-PAYMENT/host.docker.internal:service-payment:8089 with status UP (replication=true)
2025-07-30 11:38:06.650 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 11:38:32.187 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:39:06.662 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 12ms
2025-07-30 11:40:06.666 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 3ms
2025-07-30 11:41:06.668 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 1ms
2025-07-30 11:41:32.227 [Eureka-CacheFillTimer] WARN  c.n.e.r.AbstractInstanceRegistry - No remote registry available for the remote region us-east-1
2025-07-30 11:42:06.679 [Eureka-EvictionTimer] INFO  c.n.e.r.AbstractInstanceRegistry - Running the evict task with compensationTime 10ms
