2025-07-30 09:23:10.283 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 09:23:10.612 [main] INFO  o.s.c.c.c.ConfigServerConfigDataLoader - Fetching config from server at : http://localhost:8888
2025-07-30 09:23:10.619 [main] INFO  o.s.c.c.c.ConfigServerConfigDataLoader - Located environment: name=service-order, profiles=[default], label=null, version=null, state=null
2025-07-30 09:23:11.971 [main] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Fetching config from server at : http://localhost:8888
2025-07-30 09:23:12.048 [main] INFO  o.s.c.c.c.ConfigServicePropertySourceLocator - Located environment: name=service-order, profiles=[default], label=null, version=null, state=null
2025-07-30 09:23:12.064 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-configClient'}, BootstrapPropertySource {name='bootstrapProperties-overrides'}, BootstrapPropertySource {name='bootstrapProperties-classpath:/shared/service-order.yml'}, BootstrapPropertySource {name='bootstrapProperties-classpath:/shared/application.yml'}]
2025-07-30 09:23:12.108 [main] INFO  c.ebcp.order.OrderServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:23:12.217 [main] INFO  o.s.c.c.c.ConfigServerConfigDataLoader - Fetching config from server at : http://localhost:8888
2025-07-30 09:23:12.224 [main] INFO  o.s.c.c.c.ConfigServerConfigDataLoader - Located environment: name=service-order, profiles=[default], label=null, version=null, state=null
2025-07-30 09:23:15.460 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 09:23:15.594 [main] DEBUG o.s.d.r.c.RepositoryConfigurationDelegate - Scanning for JPA repositories in packages com.ebcp.order.
2025-07-30 09:23:15.669 [main] DEBUG o.s.d.r.c.RepositoryComponentProvider - Identified candidate component class: URL [jar:nested:/G:/work/mobile/server/service-order/target/service-order-0.0.1-SNAPSHOT.jar/!BOOT-INF/classes/!/com/ebcp/order/repository/OrderItemRepository.class]
2025-07-30 09:23:15.706 [main] DEBUG o.s.d.r.c.RepositoryComponentProvider - Identified candidate component class: URL [jar:nested:/G:/work/mobile/server/service-order/target/service-order-0.0.1-SNAPSHOT.jar/!BOOT-INF/classes/!/com/ebcp/order/repository/OrderRepository.class]
2025-07-30 09:23:16.199 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 551 ms. Found 2 JPA repository interfaces.
2025-07-30 09:23:17.218 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=eea66cd5-9070-38e8-a3bd-05f22ee91097
2025-07-30 09:23:18.257 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-30 09:23:18.289 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-30 09:23:20.202 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8085 (http)
2025-07-30 09:23:20.249 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8085"]
2025-07-30 09:23:20.265 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:23:20.267 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-30 09:23:20.410 [main] INFO  o.a.c.c.C.[.[localhost].[/order] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:23:20.422 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8195 ms
2025-07-30 09:23:21.673 [main] DEBUG c.e.order.config.WebSecurityConfig$1 - Filter 'userInfoLoggingFilter' configured for use
2025-07-30 09:23:21.753 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class org.postgresql.Driver found in Thread context class loader org.springframework.boot.loader.launch.LaunchedClassLoader@2a84aee7
2025-07-30 09:23:22.226 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 09:23:22.609 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.8.Final
2025-07-30 09:23:22.751 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-30 09:23:23.722 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-30 09:23:23.813 [main] DEBUG com.zaxxer.hikari.HikariConfig - OrderHikariCP - configuration:
2025-07-30 09:23:23.820 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-30 09:23:23.820 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................true
2025-07-30 09:23:23.821 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-30 09:23:23.822 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-30 09:23:23.823 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery............."SELECT 1"
2025-07-30 09:23:23.826 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-30 09:23:23.827 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-30 09:23:23.829 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-30 09:23:23.830 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-30 09:23:23.832 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-30 09:23:23.833 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."org.postgresql.Driver"
2025-07-30 09:23:23.835 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-30 09:23:23.835 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-30 09:23:23.836 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-30 09:23:23.836 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-30 09:23:23.837 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-30 09:23:23.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-30 09:23:23.841 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................***************************************
2025-07-30 09:23:23.841 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-30 09:23:23.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........60000
2025-07-30 09:23:23.843 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-30 09:23:23.843 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................15
2025-07-30 09:23:23.845 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-30 09:23:23.847 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-30 09:23:23.849 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................5
2025-07-30 09:23:23.850 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-30 09:23:23.850 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"OrderHikariCP"
2025-07-30 09:23:23.851 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-30 09:23:23.852 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-30 09:23:23.855 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-30 09:23:23.856 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-30 09:23:23.857 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-30 09:23:23.857 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............default
2025-07-30 09:23:23.858 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"postgres"
2025-07-30 09:23:23.859 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-30 09:23:23.859 [main] INFO  com.zaxxer.hikari.HikariDataSource - OrderHikariCP - Starting...
2025-07-30 09:23:24.233 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@1735e1c4
2025-07-30 09:23:24.243 [main] INFO  com.zaxxer.hikari.HikariDataSource - OrderHikariCP - Start completed.
2025-07-30 09:23:24.344 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=1, active=1, idle=0, waiting=0)
2025-07-30 09:23:24.347 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=1, active=1, idle=0, waiting=0)
2025-07-30 09:23:24.402 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-30 09:23:24.477 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@576253a9
2025-07-30 09:23:24.608 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@59a5b735
2025-07-30 09:23:24.790 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@5d9a38d9
2025-07-30 09:23:24.902 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@1898bd0e
2025-07-30 09:23:24.914 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After adding stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:23:28.212 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-30 09:23:28.404 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 09:23:28.670 [main] DEBUG o.s.d.j.r.c.JpaMetamodelMappingContextFactoryBean - Initializing JpaMetamodelMappingContext…
2025-07-30 09:23:28.920 [main] DEBUG o.s.d.j.r.c.JpaMetamodelMappingContextFactoryBean - Finished initializing JpaMetamodelMappingContext
2025-07-30 09:23:29.459 [main] DEBUG o.s.d.r.c.s.RepositoryFactorySupport - Initializing repository instance for com.ebcp.order.repository.OrderRepository…
2025-07-30 09:23:29.666 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Looking up named query Order.findByOrderNo
2025-07-30 09:23:29.679 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Did not find named query Order.findByOrderNo
2025-07-30 09:23:29.874 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Looking up named query Order.findByUserId
2025-07-30 09:23:29.877 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Did not find named query Order.findByUserId
2025-07-30 09:23:29.881 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Looking up named query Order.findByUserId
2025-07-30 09:23:29.882 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Did not find named query Order.findByUserId
2025-07-30 09:23:29.885 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Looking up named query Order.findByChargerId
2025-07-30 09:23:29.887 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Did not find named query Order.findByChargerId
2025-07-30 09:23:29.891 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Looking up named query Order.findByUserIdAndStatusOrderByPayTimeDesc
2025-07-30 09:23:29.894 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Did not find named query Order.findByUserIdAndStatusOrderByPayTimeDesc
2025-07-30 09:23:29.909 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Looking up named query Order.findByStatus
2025-07-30 09:23:29.913 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Did not find named query Order.findByStatus
2025-07-30 09:23:29.919 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Looking up named query Order.findByUserIdAndStatus
2025-07-30 09:23:29.924 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Did not find named query Order.findByUserIdAndStatus
2025-07-30 09:23:29.935 [main] DEBUG o.s.d.r.c.s.RepositoryFactorySupport - Finished creation of repository instance for com.ebcp.order.repository.OrderRepository.
2025-07-30 09:23:30.008 [main] DEBUG o.s.d.r.c.s.RepositoryFactorySupport - Initializing repository instance for com.ebcp.order.repository.OrderItemRepository…
2025-07-30 09:23:30.015 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Looking up named query OrderItem.findByOrderId
2025-07-30 09:23:30.017 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Did not find named query OrderItem.findByOrderId
2025-07-30 09:23:30.019 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Looking up named query OrderItem.deleteByOrderId
2025-07-30 09:23:30.020 [main] DEBUG o.s.d.j.repository.query.NamedQuery - Did not find named query OrderItem.deleteByOrderId
2025-07-30 09:23:30.028 [main] DEBUG o.s.d.r.c.s.RepositoryFactorySupport - Finished creation of repository instance for com.ebcp.order.repository.OrderItemRepository.
2025-07-30 09:23:30.492 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'service-charger' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 09:23:30.667 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'service-payment' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 09:23:30.933 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 7c489d9c-f691-447b-8d8c-f59abfe5a588

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-30 09:23:32.848 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 24 endpoint(s) beneath base path '/actuator'
2025-07-30 09:23:34.072 [main] INFO  o.s.c.n.e.c.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
2025-07-30 09:23:34.206 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-30 09:23:34.333 [main] INFO  o.s.c.n.eureka.InstanceInfoFactory - Setting initial instance status as: STARTING
2025-07-30 09:23:34.908 [main] INFO  o.s.c.n.e.s.EurekaServiceRegistry - Registering application SERVICE-ORDER with eureka with status UP
2025-07-30 09:23:34.909 [main] WARN  c.n.discovery.InstanceInfoReplicator - Ignoring onDemand update due to rate limiter
2025-07-30 09:23:34.910 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8085"]
2025-07-30 09:23:34.943 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8085 (http) with context path '/order'
2025-07-30 09:23:34.944 [main] INFO  o.s.c.n.e.s.EurekaAutoServiceRegistration - Updating port to 8085
2025-07-30 09:23:34.968 [main] INFO  c.ebcp.order.OrderServiceApplication - Started OrderServiceApplication in 28.286 seconds (process running for 30.331)
2025-07-30 09:23:54.362 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:23:54.363 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:23:54.364 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:24:24.371 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:24:24.372 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:24:24.373 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:24:54.381 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:24:54.381 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:24:54.383 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:25:24.398 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:25:24.399 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:25:24.402 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:25:54.406 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:25:54.406 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:25:54.407 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:26:24.414 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:26:24.415 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:26:24.415 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:26:54.420 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:26:54.420 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:26:54.421 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:27:24.435 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:27:24.435 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:27:24.436 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:27:54.442 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:27:54.442 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:27:54.443 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:28:24.451 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:28:24.451 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:28:24.452 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:28:54.462 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:28:54.462 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:28:54.463 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:29:24.469 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:29:24.469 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:29:24.470 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:29:54.476 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:29:54.476 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:29:54.477 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:30:24.491 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:30:24.491 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:30:24.492 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:30:54.504 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:30:54.505 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:30:54.506 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:31:24.511 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:31:24.511 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:31:24.512 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:31:54.523 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:31:54.524 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:31:54.524 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:32:24.529 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:32:24.529 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:32:24.530 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:32:54.530 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:32:54.531 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:32:54.531 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:33:24.544 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:33:24.544 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:33:24.545 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:33:54.549 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:33:54.549 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:33:54.550 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:34:24.553 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:34:24.553 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:34:24.554 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:34:54.568 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:34:54.568 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:34:54.569 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:35:24.578 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:35:24.578 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:35:24.579 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:35:54.586 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:35:54.586 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:35:54.587 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:36:24.602 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:36:24.602 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:36:24.603 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:36:54.614 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:36:54.615 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:36:54.615 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:37:24.623 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:37:24.623 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:37:24.624 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:37:54.636 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:37:54.636 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:37:54.637 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:38:24.650 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:38:24.651 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:38:24.651 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:38:54.658 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:38:54.658 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:38:54.659 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:39:24.666 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:39:24.666 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:39:24.667 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:39:54.675 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:39:54.675 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:39:54.676 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:40:24.691 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:40:24.691 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:40:24.692 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:40:54.699 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:40:54.699 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:40:54.700 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:41:24.703 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:41:24.703 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:41:24.704 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:41:54.706 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:41:54.707 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:41:54.707 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:42:24.719 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:42:24.719 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:42:24.720 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:42:54.730 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:42:54.730 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:42:54.731 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:43:24.736 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:43:24.736 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:43:24.737 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:43:54.745 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:43:54.746 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:43:54.747 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:44:24.758 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:44:24.759 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:44:24.763 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:44:54.769 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:44:54.770 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:44:54.770 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:45:24.777 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:45:24.778 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:45:24.778 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:45:54.780 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:45:54.780 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:45:54.781 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:46:24.793 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:46:24.793 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:46:24.794 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:46:54.805 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:46:54.806 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:46:54.807 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:47:24.810 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:47:24.811 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:47:24.812 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:47:54.827 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:47:54.827 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:47:54.828 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:48:24.833 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:48:24.833 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:48:24.834 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:48:54.843 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:48:54.843 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:48:54.844 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:49:24.845 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:49:24.845 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:49:24.846 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:49:54.853 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:49:54.853 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:49:54.854 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:50:24.868 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:50:24.868 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:50:24.869 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:50:54.879 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:50:54.879 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:50:54.880 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:51:24.892 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:51:24.892 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:51:24.893 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:51:54.894 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:51:54.894 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:51:54.895 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:52:24.910 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:52:24.910 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:52:24.911 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:52:39.897 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@59a5b735: (connection has passed maxLifetime)
2025-07-30 09:52:39.948 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@604ab1ea
2025-07-30 09:52:48.165 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@1735e1c4: (connection has passed maxLifetime)
2025-07-30 09:52:48.204 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@ad4c20e
2025-07-30 09:52:49.734 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@576253a9: (connection has passed maxLifetime)
2025-07-30 09:52:49.881 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@4133d24
2025-07-30 09:52:53.894 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@1898bd0e: (connection has passed maxLifetime)
2025-07-30 09:52:53.933 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@2ca97e
2025-07-30 09:52:54.918 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:52:54.918 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:52:54.919 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:52:57.918 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@5d9a38d9: (connection has passed maxLifetime)
2025-07-30 09:52:58.058 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@452e371f
2025-07-30 09:53:24.925 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:53:24.926 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:53:24.926 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:53:54.942 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:53:54.942 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:53:54.943 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:54:24.948 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:54:24.948 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:54:24.949 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:54:54.955 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:54:54.955 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:54:54.956 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:55:24.970 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:55:24.973 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:55:24.973 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:55:54.976 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:55:54.977 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:55:54.977 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:56:24.987 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:56:24.988 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:56:24.988 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:56:54.992 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:56:54.993 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:56:54.993 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:57:25.004 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:57:25.004 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:57:25.005 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:57:55.012 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:57:55.013 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:57:55.014 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:58:25.029 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:58:25.029 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:58:25.030 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:58:55.033 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:58:55.033 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:58:55.034 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:59:25.039 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:59:25.039 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:59:25.040 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 09:59:55.043 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:59:55.043 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 09:59:55.044 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:00:25.060 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:00:25.060 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:00:25.061 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:00:55.067 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:00:55.067 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:00:55.068 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:01:25.075 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:01:25.075 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:01:25.076 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:01:55.079 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:01:55.080 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:01:55.082 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:02:25.090 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:02:25.090 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:02:25.091 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:02:55.095 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:02:55.096 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:02:55.096 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:03:25.099 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:03:25.099 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:03:25.100 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:03:55.059 [http-nio-8085-exec-2] INFO  o.a.c.c.C.[.[localhost].[/order] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:03:55.062 [http-nio-8085-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 10:03:55.091 [http-nio-8085-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 29 ms
2025-07-30 10:03:55.105 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:03:55.106 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:03:55.107 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:04:25.109 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:04:25.109 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:04:25.110 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:04:55.121 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:04:55.121 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:04:55.122 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:05:25.127 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:05:25.127 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:05:25.128 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:05:55.129 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:05:55.130 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:05:55.130 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:06:25.140 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:06:25.142 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:06:25.144 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:06:55.149 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:06:55.150 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:06:55.151 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:07:25.161 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:07:25.161 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:07:25.162 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:07:55.178 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:07:55.179 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:07:55.180 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:08:25.180 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:08:25.180 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:08:25.181 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:08:55.194 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:08:55.194 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:08:55.195 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:09:25.202 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:09:25.202 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:09:25.203 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:09:55.216 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:09:55.216 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:09:55.217 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:10:25.221 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:10:25.222 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:10:25.222 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:10:55.234 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:10:55.234 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:10:55.235 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:11:25.237 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:11:25.238 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:11:25.239 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:11:55.247 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:11:55.248 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:11:55.249 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:12:25.264 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:12:25.265 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:12:25.265 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:12:55.270 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:12:55.271 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:12:55.271 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:13:25.278 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:13:25.278 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:13:25.279 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:13:55.290 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:13:55.290 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:13:55.291 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:14:25.291 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:14:25.291 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:14:25.292 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:14:55.303 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:14:55.303 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:14:55.303 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:15:25.307 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:15:25.307 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:15:25.307 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:15:55.323 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:15:55.323 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:15:55.324 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:16:25.331 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:16:25.331 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:16:25.332 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:16:55.346 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:16:55.346 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:16:55.347 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:17:25.355 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:17:25.355 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:17:25.356 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:17:55.358 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:17:55.359 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:17:55.359 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:18:25.361 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:18:25.362 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:18:25.362 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:18:55.368 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:18:55.369 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:18:55.370 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:19:25.385 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:19:25.385 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:19:25.386 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:19:55.395 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:19:55.395 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:19:55.396 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:20:25.403 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:20:25.403 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:20:25.404 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:20:55.405 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:20:55.406 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:20:55.406 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:21:25.414 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:21:25.415 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:21:25.416 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:21:55.425 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:21:55.425 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:21:55.426 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:22:06.257 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@4133d24: (connection has passed maxLifetime)
2025-07-30 10:22:06.310 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@4ddfe8b
2025-07-30 10:22:23.627 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@2ca97e: (connection has passed maxLifetime)
2025-07-30 10:22:23.665 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@71ee2a45
2025-07-30 10:22:25.434 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:22:25.434 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:22:25.435 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:22:31.110 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@ad4c20e: (connection has passed maxLifetime)
2025-07-30 10:22:31.154 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@7f8e88e8
2025-07-30 10:22:31.888 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@604ab1ea: (connection has passed maxLifetime)
2025-07-30 10:22:32.031 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@71497969
2025-07-30 10:22:53.133 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@452e371f: (connection has passed maxLifetime)
2025-07-30 10:22:53.177 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@40d73fc0
2025-07-30 10:22:55.438 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:22:55.439 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:22:55.439 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:23:25.441 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:23:25.442 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:23:25.443 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:23:55.447 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:23:55.447 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:23:55.448 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:24:25.464 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:24:25.464 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:24:25.465 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:24:55.466 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:24:55.466 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:24:55.467 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:25:25.467 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:25:25.468 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:25:25.469 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:25:55.476 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:25:55.476 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:25:55.477 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:26:25.492 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:26:25.493 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:26:25.493 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:26:55.503 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:26:55.504 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:26:55.504 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:27:25.516 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:27:25.516 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:27:25.517 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:27:55.517 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:27:55.518 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:27:55.518 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:28:25.522 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:28:25.522 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:28:25.523 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:28:55.536 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:28:55.536 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:28:55.537 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:29:25.551 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:29:25.552 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:29:25.552 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:29:55.565 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:29:55.565 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:29:55.566 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:30:25.578 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:30:25.578 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:30:25.579 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:30:55.587 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:30:55.587 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:30:55.588 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:31:25.593 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:31:25.593 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:31:25.594 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:31:55.603 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:31:55.603 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:31:55.604 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:32:25.616 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:32:25.620 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:32:25.623 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:32:55.629 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:32:55.629 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:32:55.630 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:33:25.641 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:33:25.641 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:33:25.642 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:33:55.656 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:33:55.657 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:33:55.658 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:34:25.659 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:34:25.660 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:34:25.661 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:34:55.668 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:34:55.668 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:34:55.669 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:35:25.680 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:35:25.682 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:35:25.682 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:35:55.686 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:35:55.686 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:35:55.687 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:36:25.693 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:36:25.693 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:36:25.694 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:36:55.701 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:36:55.701 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:36:55.702 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:37:25.714 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:37:25.714 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:37:25.715 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:37:55.716 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:37:55.716 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:37:55.717 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:38:25.721 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:38:25.721 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:38:25.722 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:38:55.730 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:38:55.730 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:38:55.731 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:39:25.743 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:39:25.743 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:39:25.744 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:39:55.750 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:39:55.751 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:39:55.752 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:40:25.754 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:40:25.754 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:40:25.755 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:40:55.766 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:40:55.766 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:40:55.767 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:41:25.779 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:41:25.779 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:41:25.780 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:41:55.781 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:41:55.781 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:41:55.782 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:42:25.788 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:42:25.788 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:42:25.789 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:42:55.797 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:42:55.797 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:42:55.798 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:43:25.814 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:43:25.815 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:43:25.816 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:43:55.826 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:43:55.826 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:43:55.827 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:44:25.829 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:44:25.829 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:44:25.830 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:44:55.846 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:44:55.846 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:44:55.847 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:45:25.856 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:45:25.856 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:45:25.857 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:45:55.871 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:45:55.872 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:45:55.872 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:46:25.879 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:46:25.880 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:46:25.888 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:46:55.892 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:46:55.892 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:46:55.893 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:47:25.906 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:47:25.906 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:47:25.907 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:47:55.913 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:47:55.913 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:47:55.921 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:48:25.930 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:48:25.930 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:48:25.931 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:48:55.940 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:48:55.941 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:48:55.943 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:49:25.959 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:49:25.960 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:49:25.961 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:49:55.970 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:49:55.970 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:49:55.971 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:50:25.984 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:50:25.984 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:50:25.985 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:50:55.993 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:50:55.994 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:50:55.994 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:51:24.252 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@4ddfe8b: (connection has passed maxLifetime)
2025-07-30 10:51:24.294 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@69ea88b4
2025-07-30 10:51:26.003 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:51:26.004 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:51:26.005 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:51:56.021 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:51:56.022 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:51:56.022 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:51:59.891 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@71ee2a45: (connection has passed maxLifetime)
2025-07-30 10:51:59.945 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@7c3638ac
2025-07-30 10:52:08.408 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@7f8e88e8: (connection has passed maxLifetime)
2025-07-30 10:52:08.486 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@55060fb8
2025-07-30 10:52:19.534 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@71497969: (connection has passed maxLifetime)
2025-07-30 10:52:19.577 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@45869f74
2025-07-30 10:52:20.621 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@40d73fc0: (connection has passed maxLifetime)
2025-07-30 10:52:20.662 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@4b4179b4
2025-07-30 10:52:26.036 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:52:26.036 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:52:26.037 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:52:56.044 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:52:56.045 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:52:56.045 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:53:26.057 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:53:26.065 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:53:26.066 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:53:56.078 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:53:56.078 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:53:56.079 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:54:26.093 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:54:26.094 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:54:26.095 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:54:56.095 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:54:56.095 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:54:56.096 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:55:26.099 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:55:26.099 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:55:26.100 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:55:56.107 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:55:56.107 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:55:56.108 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:56:26.113 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:56:26.113 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:56:26.122 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:56:56.135 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:56:56.136 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:56:56.136 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:57:26.138 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:57:26.138 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:57:26.139 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:57:56.149 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:57:56.149 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:57:56.150 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:58:26.152 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:58:26.152 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:58:26.153 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:58:56.159 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:58:56.159 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:58:56.160 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:59:26.167 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:59:26.167 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:59:26.168 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 10:59:56.179 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:59:56.180 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 10:59:56.180 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:00:26.182 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:00:26.182 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:00:26.189 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:00:56.193 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:00:56.193 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:00:56.194 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:01:26.195 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:01:26.196 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:01:26.204 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:01:56.213 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:01:56.214 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:01:56.214 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:02:26.217 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:02:26.218 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:02:26.219 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:02:56.231 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:02:56.232 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:02:56.233 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:03:26.235 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:03:26.235 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:03:26.236 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:03:56.241 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:03:56.241 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:03:56.242 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:04:26.256 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:04:26.256 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:04:26.257 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:04:56.261 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:04:56.261 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:04:56.262 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:05:26.264 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:05:26.264 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:05:26.266 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:05:56.270 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:05:56.270 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:05:56.271 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:06:26.285 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:06:26.293 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:06:26.295 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:06:56.298 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:06:56.299 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:06:56.301 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:07:26.308 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:07:26.309 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:07:26.317 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:07:56.322 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:07:56.323 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:07:56.323 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:08:26.326 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:08:26.326 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:08:26.327 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:08:56.340 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:08:56.340 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:08:56.340 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:09:26.350 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:09:26.350 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:09:26.351 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:09:56.357 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:09:56.357 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:09:56.358 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:10:26.364 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:10:26.364 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:10:26.365 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:10:56.374 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:10:56.374 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:10:56.375 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:11:26.380 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:11:26.381 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:11:26.381 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:11:56.392 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:11:56.401 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:11:56.402 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:12:26.404 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:12:26.404 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:12:26.405 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:12:56.406 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:12:56.407 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:12:56.407 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:13:26.415 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:13:26.415 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:13:26.416 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:13:56.420 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:13:56.422 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:13:56.424 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:14:26.438 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:14:26.438 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:14:26.439 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:14:56.451 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:14:56.452 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:14:56.453 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:15:26.465 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:15:26.467 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:15:26.468 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:15:56.472 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:15:56.474 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:15:56.474 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:16:26.488 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:16:26.489 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:16:26.490 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:16:56.499 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:16:56.500 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:16:56.500 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:17:26.507 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:17:26.507 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:17:26.508 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:17:56.515 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:17:56.515 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:17:56.516 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:18:26.525 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:18:26.526 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:18:26.526 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:18:56.536 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:18:56.536 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:18:56.537 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:19:26.551 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:19:26.551 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:19:26.552 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:19:56.557 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:19:56.558 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:19:56.559 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:20:26.559 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:20:26.559 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:20:26.560 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:20:56.561 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:20:56.564 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:20:56.565 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:21:06.705 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@69ea88b4: (connection has passed maxLifetime)
2025-07-30 11:21:06.771 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@6bdb95dd
2025-07-30 11:21:26.579 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:21:26.579 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:21:26.580 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:21:30.717 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@55060fb8: (connection has passed maxLifetime)
2025-07-30 11:21:30.867 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@66fd5964
2025-07-30 11:21:39.111 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@45869f74: (connection has passed maxLifetime)
2025-07-30 11:21:39.176 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@7853671d
2025-07-30 11:21:53.787 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@7c3638ac: (connection has passed maxLifetime)
2025-07-30 11:21:53.837 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@54d2923
2025-07-30 11:21:56.594 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:21:56.595 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:21:56.595 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:22:10.049 [OrderHikariCP connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - OrderHikariCP - Closing connection org.postgresql.jdbc.PgConnection@4b4179b4: (connection has passed maxLifetime)
2025-07-30 11:22:10.092 [OrderHikariCP connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Added connection org.postgresql.jdbc.PgConnection@49a85b08
2025-07-30 11:22:26.597 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:22:26.598 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:22:26.599 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:22:56.602 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:22:56.603 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:22:56.603 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:23:26.617 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:23:26.618 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:23:26.618 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:23:56.629 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:23:56.631 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:23:56.632 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:24:26.648 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:24:26.648 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:24:26.649 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:24:56.659 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:24:56.660 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:24:56.661 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:25:26.670 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:25:26.670 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:25:26.671 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:25:56.678 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:25:56.678 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:25:56.679 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:26:26.685 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:26:26.686 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:26:26.687 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:26:56.701 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:26:56.702 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:26:56.702 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:27:26.715 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:27:26.716 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:27:26.716 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:27:56.724 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:27:56.724 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:27:56.725 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:28:26.735 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:28:26.735 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:28:26.736 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:28:56.737 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:28:56.737 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:28:56.738 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:29:26.743 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:29:26.744 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:29:26.744 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:29:56.750 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:29:56.751 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:29:56.752 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:30:26.765 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:30:26.765 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:30:26.766 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:30:56.775 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:30:56.776 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:30:56.776 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:31:26.787 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:31:26.787 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:31:26.788 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:31:56.794 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:31:56.794 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:31:56.795 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:32:26.799 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:32:26.799 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:32:26.800 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:32:56.801 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:32:56.801 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:32:56.802 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:33:26.808 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:33:26.808 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:33:26.809 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:33:56.817 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:33:56.817 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:33:56.818 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:34:26.830 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:34:26.831 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:34:26.832 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:34:56.847 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:34:56.847 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:34:56.848 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:35:26.852 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:35:26.852 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:35:26.853 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:35:56.858 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:35:56.858 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:35:56.859 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:36:26.864 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:36:26.864 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:36:26.865 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:36:56.872 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:36:56.872 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:36:56.873 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:37:26.878 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:37:26.879 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:37:26.879 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:37:56.891 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:37:56.892 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:37:56.893 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:38:26.905 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:38:26.905 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:38:26.906 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:38:56.910 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:38:56.910 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:38:56.911 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:39:26.912 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:39:26.912 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:39:26.913 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:39:56.926 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:39:56.927 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:39:56.929 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:40:26.934 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:40:26.934 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:40:26.935 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:40:56.948 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:40:56.949 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:40:56.950 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:41:26.956 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:41:26.956 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:41:26.957 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-30 11:41:56.964 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Before cleanup stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:41:56.964 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - After cleanup  stats (total=5, active=0, idle=5, waiting=0)
2025-07-30 11:41:56.965 [OrderHikariCP housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - OrderHikariCP - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
