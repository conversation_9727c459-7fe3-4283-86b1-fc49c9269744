2025-07-30 09:23:10.908 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 09:23:12.819 [main] INFO  c.e.p.PaymentServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:23:16.284 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 09:23:16.831 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 511 ms. Found 4 JPA repository interfaces.
2025-07-30 09:23:19.227 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-30 09:23:19.244 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-30 09:23:20.991 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8089 (http)
2025-07-30 09:23:21.029 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8089"]
2025-07-30 09:23:21.055 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:23:21.059 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-30 09:23:21.202 [main] INFO  o.a.c.c.C.[.[localhost].[/payment] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:23:21.206 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8346 ms
2025-07-30 09:23:22.660 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 09:23:22.968 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.8.Final
2025-07-30 09:23:23.137 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-30 09:23:24.089 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-30 09:23:24.190 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-30 09:23:27.915 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-30 09:23:28.734 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 09:23:30.145 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-30 09:23:31.615 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 09:23:32.067 [main] INFO  c.e.payment.config.WechatPayConfig - 微信支付商户私钥加载成功，APIv3模式已启用
2025-07-30 09:23:32.130 [main] INFO  c.e.payment.config.WechatPayConfig - 开始创建微信支付HttpClient，connectTimeout=20000ms, readTimeout=60000ms
2025-07-30 09:23:32.131 [main] INFO  c.e.payment.config.WechatPayConfig - 设置网络系统属性完成，开始下载微信支付证书...
2025-07-30 09:23:32.532 [main] INFO  c.e.payment.config.WechatPayConfig - 微信支付HttpClient创建成功，APIv3模式已启用
2025-07-30 09:23:32.555 [main] INFO  c.e.payment.config.WechatPayConfig - 微信支付签名验证器Bean创建（懒加载模式）
2025-07-30 09:23:33.060 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: f221f16d-0f03-4bb3-9c1f-01a45874c9f0

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-30 09:23:34.541 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 24 endpoint(s) beneath base path '/actuator'
2025-07-30 09:23:35.275 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-30 09:23:35.602 [main] WARN  c.n.discovery.InstanceInfoReplicator - Ignoring onDemand update due to rate limiter
2025-07-30 09:23:35.603 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8089"]
2025-07-30 09:23:35.623 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8089 (http) with context path '/payment'
2025-07-30 09:23:35.635 [main] INFO  c.e.p.PaymentServiceApplication - Started PaymentServiceApplication in 28.508 seconds (process running for 30.755)
2025-07-30 10:03:58.441 [http-nio-8089-exec-1] INFO  o.a.c.c.C.[.[localhost].[/payment] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:06:37.163 [http-nio-8089-exec-1] INFO  c.e.p.c.MiniAppPaymentController - 计算支付方案：用户ID=46, 金额=0.02, 优先使用余额=true
2025-07-30 10:06:37.171 [http-nio-8089-exec-1] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 计算支付方案：用户ID=46, 总金额=0.02, 优先使用余额=true
2025-07-30 10:06:37.172 [http-nio-8089-exec-1] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 10:06:37.287 [http-nio-8089-exec-1] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 10:06:37.290 [http-nio-8089-exec-1] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 用户当前余额：90.38
2025-07-30 10:06:37.291 [http-nio-8089-exec-1] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 余额充足，使用余额支付：0.02
2025-07-30 10:06:42.173 [http-nio-8089-exec-4] INFO  c.e.p.c.MiniAppPaymentController - 收到混合支付请求：MixedPaymentRequest(userId=null, totalAmount=0.02, balanceAmount=0, wechatAmount=0.02, description=充电0.02, relatedType=CHARGING, relatedId=0, orderNo=CH1753841202709902, useBalanceFirst=false, chargerId=13, socketId=1, time=480)
2025-07-30 10:06:42.179 [http-nio-8089-exec-4] INFO  c.e.p.c.MiniAppPaymentController - 处理混合支付：用户ID=46, 充电桩ID=13, 总金额=0.02
2025-07-30 10:06:42.240 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 [混合支付] 开始处理混合支付：用户ID=46, 总金额=0.02, 余额金额=0, 微信金额=0.02
2025-07-30 10:06:42.241 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔍 [预检查] 开始支付预检查
2025-07-30 10:06:42.241 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 10:06:42.251 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 10:06:42.253 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 10:06:42.658 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 10:06:42.698 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 10:06:42.698 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 10:06:42.708 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 10:06:42.710 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 10:06:42.711 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [预检查] 所有检查通过
2025-07-30 10:06:42.711 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 预检查通过，开始创建支付记录
2025-07-30 10:06:42.712 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📝 [创建记录] 开始在独立事务中创建支付记录
2025-07-30 10:06:42.712 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 10:06:42.720 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 10:06:42.722 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电桩ID：13
2025-07-30 10:06:42.723 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加端口号：1
2025-07-30 10:06:42.724 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电时长：480分钟
2025-07-30 10:06:42.724 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 10:06:42.744 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 10:06:42.747 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 10:06:42.747 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 创建微信支付记录：用户ID=46, 金额=0.02元, 充电桩ID=13, 端口=1, 时长=480分钟, 备注=微信支付：充电0.02；充电桩ID：13；端口：1；时长：480分钟；支付配置：捷运通达；子商户：1719414033；订单号：CH1753841202709902 - 微信支付订单创建成功（待确认）
2025-07-30 10:06:42.813 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 微信支付记录创建成功：ID=591
2025-07-30 10:06:42.814 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 所有支付记录创建完成：[591]
2025-07-30 10:06:42.815 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 支付记录创建成功：[591]
2025-07-30 10:06:42.816 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 💳 [微信支付] 开始安全处理微信支付
2025-07-30 10:06:42.817 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 10:06:42.820 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 10:06:42.821 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 10:06:42.822 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 10:06:42.829 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 10:06:42.831 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 10:06:42.831 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📞 [微信支付] 调用微信支付API创建订单
2025-07-30 10:06:42.832 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.WechatPayServiceImpl - 创建微信小程序支付订单：订单号=WX175384120272046, 金额=0.02, 描述=充电0.02, openId=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 子商户ID=1719414033
2025-07-30 10:06:42.833 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.WechatPayServiceImpl - 原始描述: 充电0.02 -> 清理后描述: 充电0.02
2025-07-30 10:06:42.834 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.WechatPayServiceImpl - 使用指定子商户ID: 1719414033
2025-07-30 10:06:42.937 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3请求参数：{"sp_appid":"wx9102c8b1b86d0671","amount":{"total":2,"currency":"CNY"},"out_trade_no":"WX175384120272046","sp_mchid":"1497188322","description":"充电0.02","sub_mchid":"1719414033","notify_url":"https://evcloud.yparks.cn/gateway/payment/wechat/notify","payer":{"sp_openid":"oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4"},"scene_info":{"payer_client_ip":"127.0.0.1"}}
2025-07-30 10:06:43.864 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3响应：状态码=200, 响应体={"prepay_id":"wx3010064501247351b6d3f9f9981b0e0001"}
2025-07-30 10:06:43.914 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [记录更新] 微信支付记录更新成功：ID=591, prepayId=wx3010064501247351b6d3f9f9981b0e0001
2025-07-30 10:06:43.915 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [微信支付] 微信支付订单创建成功：prepayId=wx3010064501247351b6d3f9f9981b0e0001
2025-07-30 10:06:43.915 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 处理完成：总金额=0.02, 支付记录=[591]
2025-07-30 10:09:22.008 [http-nio-8089-exec-5] INFO  c.e.p.c.MiniAppPaymentController - 收到混合支付请求：MixedPaymentRequest(userId=null, totalAmount=0.02, balanceAmount=0, wechatAmount=0.02, description=充电0.02, relatedType=CHARGING, relatedId=0, orderNo=CH1753841362545423, useBalanceFirst=false, chargerId=13, socketId=1, time=480)
2025-07-30 10:09:22.010 [http-nio-8089-exec-5] INFO  c.e.p.c.MiniAppPaymentController - 处理混合支付：用户ID=46, 充电桩ID=13, 总金额=0.02
2025-07-30 10:09:22.012 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 [混合支付] 开始处理混合支付：用户ID=46, 总金额=0.02, 余额金额=0, 微信金额=0.02
2025-07-30 10:09:22.014 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔍 [预检查] 开始支付预检查
2025-07-30 10:09:22.014 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 10:09:22.036 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 10:09:22.037 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 10:09:22.041 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 10:09:22.044 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 10:09:22.044 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 10:09:22.051 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 10:09:22.052 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 10:09:22.053 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [预检查] 所有检查通过
2025-07-30 10:09:22.053 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 预检查通过，开始创建支付记录
2025-07-30 10:09:22.054 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📝 [创建记录] 开始在独立事务中创建支付记录
2025-07-30 10:09:22.054 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 10:09:22.064 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 10:09:22.064 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电桩ID：13
2025-07-30 10:09:22.065 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加端口号：1
2025-07-30 10:09:22.065 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电时长：480分钟
2025-07-30 10:09:22.066 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 10:09:22.068 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 10:09:22.069 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 10:09:22.070 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 创建微信支付记录：用户ID=46, 金额=0.02元, 充电桩ID=13, 端口=1, 时长=480分钟, 备注=微信支付：充电0.02；充电桩ID：13；端口：1；时长：480分钟；支付配置：捷运通达；子商户：1719414033；订单号：CH1753841362545423 - 微信支付订单创建成功（待确认）
2025-07-30 10:09:22.073 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 微信支付记录创建成功：ID=592
2025-07-30 10:09:22.074 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 所有支付记录创建完成：[592]
2025-07-30 10:09:22.076 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 支付记录创建成功：[592]
2025-07-30 10:09:22.077 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 💳 [微信支付] 开始安全处理微信支付
2025-07-30 10:09:22.078 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 10:09:22.082 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 10:09:22.083 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 10:09:22.083 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 10:09:22.095 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 10:09:22.096 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 10:09:22.096 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📞 [微信支付] 调用微信支付API创建订单
2025-07-30 10:09:22.097 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 创建微信小程序支付订单：订单号=WX175384136206446, 金额=0.02, 描述=充电0.02, openId=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 子商户ID=1719414033
2025-07-30 10:09:22.098 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 原始描述: 充电0.02 -> 清理后描述: 充电0.02
2025-07-30 10:09:22.099 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 使用指定子商户ID: 1719414033
2025-07-30 10:09:22.099 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3请求参数：{"sp_appid":"wx9102c8b1b86d0671","amount":{"total":2,"currency":"CNY"},"out_trade_no":"WX175384136206446","sp_mchid":"1497188322","description":"充电0.02","sub_mchid":"1719414033","notify_url":"https://evcloud.yparks.cn/gateway/payment/wechat/notify","payer":{"sp_openid":"oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4"},"scene_info":{"payer_client_ip":"127.0.0.1"}}
2025-07-30 10:09:22.568 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3响应：状态码=200, 响应体={"prepay_id":"wx30100923722520281111d9c11f68510001"}
2025-07-30 10:09:22.574 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [记录更新] 微信支付记录更新成功：ID=592, prepayId=wx30100923722520281111d9c11f68510001
2025-07-30 10:09:22.575 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [微信支付] 微信支付订单创建成功：prepayId=wx30100923722520281111d9c11f68510001
2025-07-30 10:09:22.575 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 处理完成：总金额=0.02, 支付记录=[592]
2025-07-30 10:12:12.595 [http-nio-8089-exec-4] INFO  c.e.p.c.MiniAppPaymentController - 计算支付方案：用户ID=46, 金额=0.02, 优先使用余额=true
2025-07-30 10:12:12.600 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 计算支付方案：用户ID=46, 总金额=0.02, 优先使用余额=true
2025-07-30 10:12:12.601 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 10:12:12.612 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 10:12:12.612 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 用户当前余额：90.38
2025-07-30 10:12:12.613 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 余额充足，使用余额支付：0.02
2025-07-30 10:12:19.426 [http-nio-8089-exec-5] INFO  c.e.p.c.MiniAppPaymentController - 收到混合支付请求：MixedPaymentRequest(userId=null, totalAmount=0.02, balanceAmount=0, wechatAmount=0.02, description=充电0.02, relatedType=CHARGING, relatedId=0, orderNo=CH1753841540011917, useBalanceFirst=false, chargerId=13, socketId=1, time=480)
2025-07-30 10:12:19.426 [http-nio-8089-exec-5] INFO  c.e.p.c.MiniAppPaymentController - 处理混合支付：用户ID=46, 充电桩ID=13, 总金额=0.02
2025-07-30 10:12:19.428 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 [混合支付] 开始处理混合支付：用户ID=46, 总金额=0.02, 余额金额=0, 微信金额=0.02
2025-07-30 10:12:19.428 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔍 [预检查] 开始支付预检查
2025-07-30 10:12:19.428 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 10:12:19.444 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 10:12:19.444 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 10:12:19.449 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 10:12:19.452 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 10:12:19.452 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 10:12:19.463 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 10:12:19.463 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 10:12:19.464 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [预检查] 所有检查通过
2025-07-30 10:12:19.465 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 预检查通过，开始创建支付记录
2025-07-30 10:12:19.465 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📝 [创建记录] 开始在独立事务中创建支付记录
2025-07-30 10:12:19.466 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 10:12:19.473 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 10:12:19.473 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电桩ID：13
2025-07-30 10:12:19.474 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加端口号：1
2025-07-30 10:12:19.475 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电时长：480分钟
2025-07-30 10:12:19.475 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 10:12:19.479 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 10:12:19.480 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 10:12:19.480 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 创建微信支付记录：用户ID=46, 金额=0.02元, 充电桩ID=13, 端口=1, 时长=480分钟, 备注=微信支付：充电0.02；充电桩ID：13；端口：1；时长：480分钟；支付配置：捷运通达；子商户：1719414033；订单号：CH1753841540011917 - 微信支付订单创建成功（待确认）
2025-07-30 10:12:19.483 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 微信支付记录创建成功：ID=593
2025-07-30 10:12:19.483 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 所有支付记录创建完成：[593]
2025-07-30 10:12:19.484 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 支付记录创建成功：[593]
2025-07-30 10:12:19.484 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 💳 [微信支付] 开始安全处理微信支付
2025-07-30 10:12:19.485 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 10:12:19.488 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 10:12:19.488 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 10:12:19.489 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 10:12:19.497 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 10:12:19.497 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 10:12:19.498 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📞 [微信支付] 调用微信支付API创建订单
2025-07-30 10:12:19.498 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 创建微信小程序支付订单：订单号=WX175384153947346, 金额=0.02, 描述=充电0.02, openId=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 子商户ID=1719414033
2025-07-30 10:12:19.499 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 原始描述: 充电0.02 -> 清理后描述: 充电0.02
2025-07-30 10:12:19.499 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 使用指定子商户ID: 1719414033
2025-07-30 10:12:19.500 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3请求参数：{"sp_appid":"wx9102c8b1b86d0671","amount":{"total":2,"currency":"CNY"},"out_trade_no":"WX175384153947346","sp_mchid":"1497188322","description":"充电0.02","sub_mchid":"1719414033","notify_url":"https://evcloud.yparks.cn/gateway/payment/wechat/notify","payer":{"sp_openid":"oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4"},"scene_info":{"payer_client_ip":"127.0.0.1"}}
2025-07-30 10:12:19.932 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3响应：状态码=200, 响应体={"prepay_id":"wx301012211203511e5901bf6d74db050000"}
2025-07-30 10:12:19.957 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [记录更新] 微信支付记录更新成功：ID=593, prepayId=wx301012211203511e5901bf6d74db050000
2025-07-30 10:12:19.967 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [微信支付] 微信支付订单创建成功：prepayId=wx301012211203511e5901bf6d74db050000
2025-07-30 10:12:19.968 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 处理完成：总金额=0.02, 支付记录=[593]
2025-07-30 11:04:45.191 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 11:04:45.730 [main] INFO  c.e.p.PaymentServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-30 11:04:46.564 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 11:04:46.693 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 119 ms. Found 4 JPA repository interfaces.
2025-07-30 11:04:47.380 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-30 11:04:47.388 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-30 11:04:47.742 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8089 (http)
2025-07-30 11:04:47.753 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8089"]
2025-07-30 11:04:47.756 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 11:04:47.756 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-30 11:04:47.794 [main] INFO  o.a.c.c.C.[.[localhost].[/payment] - Initializing Spring embedded WebApplicationContext
2025-07-30 11:04:47.795 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2048 ms
2025-07-30 11:04:48.269 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 11:04:48.328 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.8.Final
2025-07-30 11:04:48.364 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-30 11:04:48.570 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-30 11:04:48.597 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-30 11:04:49.441 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-30 11:04:49.644 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 11:04:50.042 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-30 11:04:50.584 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 11:04:50.770 [main] INFO  c.e.payment.config.WechatPayConfig - 微信支付商户私钥加载成功，APIv3模式已启用
2025-07-30 11:04:50.777 [main] INFO  c.e.payment.config.WechatPayConfig - 开始创建微信支付HttpClient，connectTimeout=20000ms, readTimeout=60000ms
2025-07-30 11:04:50.778 [main] INFO  c.e.payment.config.WechatPayConfig - 设置网络系统属性完成，开始下载微信支付证书...
2025-07-30 11:04:50.896 [main] INFO  c.e.payment.config.WechatPayConfig - 微信支付HttpClient创建成功，APIv3模式已启用
2025-07-30 11:04:50.908 [main] INFO  c.e.payment.config.WechatPayConfig - 微信支付签名验证器Bean创建（懒加载模式）
2025-07-30 11:04:51.176 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: bb1b64ab-aa35-4b56-8afb-ab7e597f556f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-30 11:04:52.065 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 24 endpoint(s) beneath base path '/actuator'
2025-07-30 11:04:52.620 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-30 11:04:52.904 [main] WARN  c.n.discovery.InstanceInfoReplicator - Ignoring onDemand update due to rate limiter
2025-07-30 11:04:52.906 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8089"]
2025-07-30 11:04:52.930 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8089 (http) with context path '/payment'
2025-07-30 11:04:52.946 [main] INFO  c.e.p.PaymentServiceApplication - Started PaymentServiceApplication in 8.616 seconds (process running for 9.057)
2025-07-30 11:05:40.306 [http-nio-8089-exec-2] INFO  o.a.c.c.C.[.[localhost].[/payment] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:05:50.764 [http-nio-8089-exec-2] INFO  c.e.p.c.MiniAppPaymentController - 计算支付方案：用户ID=46, 金额=0.02, 优先使用余额=true
2025-07-30 11:05:50.765 [http-nio-8089-exec-2] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 计算支付方案：用户ID=46, 总金额=0.02, 优先使用余额=true
2025-07-30 11:05:50.768 [http-nio-8089-exec-2] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 11:05:50.781 [http-nio-8089-exec-2] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 11:05:50.782 [http-nio-8089-exec-2] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 用户当前余额：90.38
2025-07-30 11:05:50.783 [http-nio-8089-exec-2] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 余额充足，使用余额支付：0.02
2025-07-30 11:05:55.042 [http-nio-8089-exec-5] INFO  c.e.p.c.MiniAppPaymentController - 收到混合支付请求：MixedPaymentRequest(userId=null, totalAmount=0.02, balanceAmount=0, wechatAmount=0.02, description=充电0.02, relatedType=CHARGING, relatedId=0, orderNo=CH1753844755850394, useBalanceFirst=false, chargerId=13, socketId=1, time=480)
2025-07-30 11:05:55.043 [http-nio-8089-exec-5] INFO  c.e.p.c.MiniAppPaymentController - 处理混合支付：用户ID=46, 充电桩ID=13, 总金额=0.02
2025-07-30 11:05:55.050 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 [混合支付] 开始处理混合支付：用户ID=46, 总金额=0.02, 余额金额=0, 微信金额=0.02
2025-07-30 11:05:55.051 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔍 [预检查] 开始支付预检查
2025-07-30 11:05:55.051 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 11:05:55.057 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 11:05:55.057 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 11:05:55.133 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 11:05:55.146 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 11:05:55.147 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 11:05:55.162 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 11:05:55.163 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 11:05:55.163 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [预检查] 所有检查通过
2025-07-30 11:05:55.163 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 预检查通过，开始创建支付记录
2025-07-30 11:05:55.164 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📝 [创建记录] 开始在独立事务中创建支付记录
2025-07-30 11:05:55.164 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 11:05:55.170 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 11:05:55.174 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电桩ID：13
2025-07-30 11:05:55.175 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加端口号：1
2025-07-30 11:05:55.175 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电时长：480分钟
2025-07-30 11:05:55.176 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 11:05:55.185 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 11:05:55.189 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 11:05:55.189 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 创建微信支付记录：用户ID=46, 金额=0.02元, 充电桩ID=13, 端口=1, 时长=480分钟, 备注=微信支付：充电0.02；充电桩ID：13；端口：1；时长：480分钟；支付配置：捷运通达；子商户：1719414033；订单号：CH1753844755850394 - 微信支付订单创建成功（待确认）
2025-07-30 11:05:55.210 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 微信支付记录创建成功：ID=594
2025-07-30 11:05:55.210 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 所有支付记录创建完成：[594]
2025-07-30 11:05:55.211 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 支付记录创建成功：[594]
2025-07-30 11:05:55.211 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 💳 [微信支付] 开始安全处理微信支付
2025-07-30 11:05:55.212 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 11:05:55.214 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 11:05:55.218 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 11:05:55.219 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 11:05:55.224 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 11:05:55.224 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 11:05:55.224 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📞 [微信支付] 调用微信支付API创建订单
2025-07-30 11:05:55.225 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 创建微信小程序支付订单：订单号=WX175384475517346, 金额=0.02, 描述=充电0.02, openId=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 子商户ID=1719414033
2025-07-30 11:05:55.226 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 原始描述: 充电0.02 -> 清理后描述: 充电0.02
2025-07-30 11:05:55.226 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 使用指定子商户ID: 1719414033
2025-07-30 11:05:55.263 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3请求参数：{"sp_appid":"wx9102c8b1b86d0671","amount":{"total":2,"currency":"CNY"},"out_trade_no":"WX175384475517346","sp_mchid":"1497188322","description":"充电0.02","sub_mchid":"1719414033","notify_url":"https://evcloud.yparks.cn/gateway/payment/wechat/notify","payer":{"sp_openid":"oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4"},"scene_info":{"payer_client_ip":"127.0.0.1"}}
2025-07-30 11:05:56.402 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3响应：状态码=200, 响应体={"prepay_id":"wx30110557821235b3b8a4204b41ecf70000"}
2025-07-30 11:05:56.434 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [记录更新] 微信支付记录更新成功：ID=594, prepayId=wx30110557821235b3b8a4204b41ecf70000
2025-07-30 11:05:56.435 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [微信支付] 微信支付订单创建成功：prepayId=wx30110557821235b3b8a4204b41ecf70000
2025-07-30 11:05:56.435 [http-nio-8089-exec-5] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 处理完成：总金额=0.02, 支付记录=[594]
2025-07-30 11:37:54.884 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 11:37:55.492 [main] INFO  c.e.p.PaymentServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-30 11:37:56.285 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 11:37:56.403 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 110 ms. Found 4 JPA repository interfaces.
2025-07-30 11:37:57.012 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-30 11:37:57.017 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-30 11:37:57.382 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8089 (http)
2025-07-30 11:37:57.393 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8089"]
2025-07-30 11:37:57.395 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 11:37:57.395 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-30 11:37:57.428 [main] INFO  o.a.c.c.C.[.[localhost].[/payment] - Initializing Spring embedded WebApplicationContext
2025-07-30 11:37:57.429 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1918 ms
2025-07-30 11:37:57.943 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 11:37:58.003 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.8.Final
2025-07-30 11:37:58.033 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-30 11:37:58.217 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-30 11:37:58.242 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-30 11:37:59.016 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-30 11:37:59.214 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 11:37:59.628 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-30 11:38:00.042 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 11:38:00.203 [main] INFO  c.e.payment.config.WechatPayConfig - 微信支付商户私钥加载成功，APIv3模式已启用
2025-07-30 11:38:00.209 [main] INFO  c.e.payment.config.WechatPayConfig - 开始创建微信支付HttpClient，connectTimeout=20000ms, readTimeout=60000ms
2025-07-30 11:38:00.210 [main] INFO  c.e.payment.config.WechatPayConfig - 设置网络系统属性完成，开始下载微信支付证书...
2025-07-30 11:38:00.313 [main] INFO  c.e.payment.config.WechatPayConfig - 微信支付HttpClient创建成功，APIv3模式已启用
2025-07-30 11:38:00.322 [main] INFO  c.e.payment.config.WechatPayConfig - 微信支付签名验证器Bean创建（懒加载模式）
2025-07-30 11:38:00.565 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a5831c3f-5a29-4fe0-8984-af0f566b155e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-30 11:38:01.244 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 24 endpoint(s) beneath base path '/actuator'
2025-07-30 11:38:01.737 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-30 11:38:01.998 [main] WARN  c.n.discovery.InstanceInfoReplicator - Ignoring onDemand update due to rate limiter
2025-07-30 11:38:01.999 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8089"]
2025-07-30 11:38:02.015 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8089 (http) with context path '/payment'
2025-07-30 11:38:02.029 [main] INFO  c.e.p.PaymentServiceApplication - Started PaymentServiceApplication in 8.162 seconds (process running for 8.653)
2025-07-30 11:38:40.637 [http-nio-8089-exec-1] INFO  o.a.c.c.C.[.[localhost].[/payment] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:40:33.646 [http-nio-8089-exec-4] INFO  c.e.p.c.MiniAppPaymentController - 计算支付方案：用户ID=46, 金额=0.02, 优先使用余额=true
2025-07-30 11:40:33.647 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 计算支付方案：用户ID=46, 总金额=0.02, 优先使用余额=true
2025-07-30 11:40:33.651 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 11:40:33.712 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 11:40:33.713 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 用户当前余额：90.38
2025-07-30 11:40:33.715 [http-nio-8089-exec-4] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 余额充足，使用余额支付：0.02
2025-07-30 11:40:38.056 [http-nio-8089-exec-3] INFO  c.e.p.c.MiniAppPaymentController - 收到混合支付请求：MixedPaymentRequest(userId=null, totalAmount=0.02, balanceAmount=0, wechatAmount=0.02, description=充电0.02, relatedType=CHARGING, relatedId=0, orderNo=CH175384683898390, useBalanceFirst=false, chargerId=13, socketId=1, time=480)
2025-07-30 11:40:38.059 [http-nio-8089-exec-3] INFO  c.e.p.c.MiniAppPaymentController - 处理混合支付：用户ID=46, 充电桩ID=13, 总金额=0.02
2025-07-30 11:40:38.085 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 [混合支付] 开始处理混合支付：用户ID=46, 总金额=0.02, 余额金额=0, 微信金额=0.02
2025-07-30 11:40:38.089 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔍 [预检查] 开始支付预检查
2025-07-30 11:40:38.090 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 11:40:38.100 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 11:40:38.101 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 11:40:38.392 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 11:40:38.429 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 11:40:38.435 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 11:40:38.445 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 11:40:38.446 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 11:40:38.447 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [预检查] 所有检查通过
2025-07-30 11:40:38.448 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 预检查通过，开始创建支付记录
2025-07-30 11:40:38.450 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📝 [创建记录] 开始在独立事务中创建支付记录
2025-07-30 11:40:38.451 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户信息：用户ID=46
2025-07-30 11:40:38.459 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户信息：MixedPaymentResponse.UserInfo(id=46, username=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, fullname=云端车牌识别～沈泽颖, avatar=https://evcloud.yparks.cn/users/uploads/avatars/oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4.jpeg, balance=90.38)
2025-07-30 11:40:38.469 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电桩ID：13
2025-07-30 11:40:38.471 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加端口号：1
2025-07-30 11:40:38.472 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 微信支付记录中添加充电时长：480分钟
2025-07-30 11:40:38.472 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 11:40:38.496 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 11:40:38.500 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 11:40:38.501 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 🔧 创建微信支付记录：用户ID=46, 金额=0.02元, 充电桩ID=13, 端口=1, 时长=480分钟, 备注=微信支付：充电0.02；充电桩ID：13；端口：1；时长：480分钟；支付配置：捷运通达；子商户：1719414033；订单号：CH175384683898390 - 微信支付订单创建成功（待确认）
2025-07-30 11:40:38.547 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 微信支付记录创建成功：ID=595
2025-07-30 11:40:38.550 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [创建记录] 所有支付记录创建完成：[595]
2025-07-30 11:40:38.551 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 支付记录创建成功：[595]
2025-07-30 11:40:38.554 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 💳 [微信支付] 开始安全处理微信支付
2025-07-30 11:40:38.555 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取充电桩13的支付配置
2025-07-30 11:40:38.560 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 充电桩13绑定了支付编码ID: 4
2025-07-30 11:40:38.561 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 找到支付编码配置: 捷运通达, 子商户ID: 1719414033
2025-07-30 11:40:38.562 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取用户微信openId：用户ID=46
2025-07-30 11:40:38.571 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 从username字段获取到微信openId
2025-07-30 11:40:38.572 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 获取到用户微信openId：oSJ-w7***
2025-07-30 11:40:38.572 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - 📞 [微信支付] 调用微信支付API创建订单
2025-07-30 11:40:38.573 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.WechatPayServiceImpl - 创建微信小程序支付订单：订单号=WX175384683846746, 金额=0.02, 描述=充电0.02, openId=oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4, 子商户ID=1719414033
2025-07-30 11:40:38.575 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.WechatPayServiceImpl - 原始描述: 充电0.02 -> 清理后描述: 充电0.02
2025-07-30 11:40:38.576 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.WechatPayServiceImpl - 使用指定子商户ID: 1719414033
2025-07-30 11:40:38.662 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3请求参数：{"sp_appid":"wx9102c8b1b86d0671","amount":{"total":2,"currency":"CNY"},"out_trade_no":"WX175384683846746","sp_mchid":"1497188322","description":"充电0.02","sub_mchid":"1719414033","notify_url":"https://evcloud.yparks.cn/gateway/payment/wechat/notify","payer":{"sp_openid":"oSJ-w7WkEP7RBgcfeLICxU7Jb5Q4"},"scene_info":{"payer_client_ip":"127.0.0.1"}}
2025-07-30 11:40:40.302 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.WechatPayServiceImpl - 微信支付APIv3响应：状态码=200, 响应体={"prepay_id":"wx3011404182052001c8ca03a4a8a94d0001"}
2025-07-30 11:40:40.394 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [记录更新] 微信支付记录更新成功：ID=595, prepayId=wx3011404182052001c8ca03a4a8a94d0001
2025-07-30 11:40:40.437 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [微信支付] 微信支付订单创建成功：prepayId=wx3011404182052001c8ca03a4a8a94d0001
2025-07-30 11:40:40.440 [http-nio-8089-exec-3] INFO  c.e.p.s.impl.MixedPaymentServiceImpl - ✅ [混合支付] 处理完成：总金额=0.02, 支付记录=[595]
